"""
K-Fold Cross-Validation Analysis untuk Deteksi Overfitting
Membuat grafik dengan X = jumlah fold, Y = akurasi untuk setiap model

Tujuan:
1. Menganalisis stabilitas model dengan berbagai nilai k (2-20)
2. Mendeteksi overfitting dengan melihat variance akurasi
3. Membandingkan performa train vs validation
4. Menentukan k optimal untuk setiap model
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import StratifiedKFold, cross_validate, train_test_split
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score
from pathlib import Path
import logging
import warnings
warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import XGBoost
try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    logger.warning("XGBoost not available")

class KFoldOverfittingAnalyzer:
    """Analyzer untuk mendeteksi overfitting menggunakan K-Fold CV"""
    
    def __init__(self, data_path, target_column='fatigue_risk'):
        """Initialize analyzer"""
        self.data_path = data_path
        self.target_column = target_column
        
        # Load data
        logger.info(f"Loading data from: {data_path}")
        self.data = pd.read_csv(data_path)
        
        # Prepare features and target
        self.X = self.data.drop(columns=[target_column])
        self.y = self.data[target_column]

        # Encode target for XGBoost (needs numeric labels)
        from sklearn.preprocessing import LabelEncoder
        self.label_encoder = LabelEncoder()
        self.y_encoded = self.label_encoder.fit_transform(self.y)
        
        logger.info(f"Dataset shape: {self.X.shape}")
        logger.info(f"Target distribution: {self.y.value_counts().to_dict()}")
        
        # Define models
        self.models = self._define_models()
        
        # Results storage
        self.results = {}
        
        # K values to test
        self.k_values = list(range(2, 21))  # k = 2 to 20
        
    def _define_models(self):
        """Define models to test"""
        models = {
            'Logistic Regression': Pipeline([
                ('scaler', StandardScaler()),
                ('lr', LogisticRegression(random_state=42, max_iter=1000))
            ]),
            'Random Forest': RandomForestClassifier(
                n_estimators=100, 
                random_state=42,
                max_depth=10  # Limit depth to prevent overfitting
            ),
            'Gradient Boosting': GradientBoostingClassifier(
                n_estimators=100,
                random_state=42,
                max_depth=6,  # Limit depth
                learning_rate=0.1
            )
        }
        
        # Add XGBoost if available
        if XGBOOST_AVAILABLE:
            models['XGBoost'] = XGBClassifier(
                n_estimators=100,
                random_state=42,
                max_depth=6,
                learning_rate=0.1,
                eval_metric='logloss'
            )
        
        return models
    
    def analyze_kfold_performance(self):
        """Analyze performance across different k values"""
        logger.info("Starting K-Fold analysis...")
        
        for model_name, model in self.models.items():
            logger.info(f"Analyzing {model_name}...")
            
            model_results = {
                'k_values': [],
                'train_means': [],
                'train_stds': [],
                'val_means': [],
                'val_stds': [],
                'train_scores_all': [],
                'val_scores_all': []
            }
            
            for k in self.k_values:
                logger.info(f"  Testing k={k}...")
                
                # Skip if k is larger than smallest class
                min_class_size = self.y.value_counts().min()
                if k > min_class_size:
                    logger.warning(f"  Skipping k={k} (larger than smallest class: {min_class_size})")
                    continue
                
                try:
                    # Stratified K-Fold
                    cv = StratifiedKFold(n_splits=k, shuffle=True, random_state=42)

                    # Use encoded labels for XGBoost, original for others
                    y_to_use = self.y_encoded if 'XGBoost' in model_name else self.y

                    # Cross-validation with train and validation scores
                    cv_results = cross_validate(
                        model, self.X, y_to_use,
                        cv=cv,
                        scoring='accuracy',
                        return_train_score=True,
                        n_jobs=-1
                    )
                    
                    # Store results
                    train_scores = cv_results['train_score']
                    val_scores = cv_results['test_score']
                    
                    model_results['k_values'].append(k)
                    model_results['train_means'].append(train_scores.mean())
                    model_results['train_stds'].append(train_scores.std())
                    model_results['val_means'].append(val_scores.mean())
                    model_results['val_stds'].append(val_scores.std())
                    model_results['train_scores_all'].append(train_scores)
                    model_results['val_scores_all'].append(val_scores)
                    
                    logger.info(f"    Train: {train_scores.mean():.4f} ± {train_scores.std():.4f}")
                    logger.info(f"    Val:   {val_scores.mean():.4f} ± {val_scores.std():.4f}")
                    
                except Exception as e:
                    logger.error(f"    Error with k={k}: {str(e)}")
                    continue
            
            self.results[model_name] = model_results
            logger.info(f"✅ Completed {model_name}")
    
    def detect_overfitting(self):
        """Detect overfitting patterns"""
        logger.info("Analyzing overfitting patterns...")
        
        overfitting_analysis = {}
        
        for model_name, results in self.results.items():
            analysis = {
                'avg_train_val_gap': [],
                'max_train_val_gap': 0,
                'min_train_val_gap': float('inf'),
                'variance_trend': [],
                'optimal_k': None,
                'overfitting_score': 0
            }
            
            train_means = np.array(results['train_means'])
            val_means = np.array(results['val_means'])
            val_stds = np.array(results['val_stds'])

            # Skip if no results (e.g., XGBoost failed)
            if len(train_means) == 0:
                logger.warning(f"No results for {model_name}, skipping overfitting analysis")
                continue

            # Calculate train-validation gap
            train_val_gaps = train_means - val_means
            analysis['avg_train_val_gap'] = train_val_gaps.tolist()
            analysis['max_train_val_gap'] = train_val_gaps.max()
            analysis['min_train_val_gap'] = train_val_gaps.min()
            
            # Variance trend (higher variance = more overfitting risk)
            analysis['variance_trend'] = val_stds.tolist()
            
            # Find optimal k (highest validation score with reasonable variance)
            # Balance between high accuracy and low variance
            val_scores = val_means - 0.5 * val_stds  # Penalize high variance
            optimal_idx = np.argmax(val_scores)
            analysis['optimal_k'] = results['k_values'][optimal_idx]
            
            # Overfitting score (0-100, higher = more overfitting)
            # Based on train-val gap and variance
            gap_score = (train_val_gaps.mean() / train_means.mean()) * 100
            variance_score = (val_stds.mean() / val_means.mean()) * 100
            analysis['overfitting_score'] = (gap_score + variance_score) / 2
            
            overfitting_analysis[model_name] = analysis
            
            logger.info(f"{model_name}:")
            logger.info(f"  Optimal k: {analysis['optimal_k']}")
            logger.info(f"  Overfitting score: {analysis['overfitting_score']:.2f}")
            logger.info(f"  Max train-val gap: {analysis['max_train_val_gap']:.4f}")
        
        return overfitting_analysis
    
    def create_kfold_plots(self, save_plots=True):
        """Create comprehensive K-Fold analysis plots"""
        logger.info("Creating K-Fold analysis plots...")
        
        # Create output directory
        output_dir = Path("results/kfold_analysis")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 1. Main K-Fold Performance Plot
        self._plot_kfold_performance(output_dir if save_plots else None)
        
        # 2. Train vs Validation Gap Analysis
        self._plot_train_val_gap(output_dir if save_plots else None)
        
        # 3. Variance Analysis
        self._plot_variance_analysis(output_dir if save_plots else None)
        
        # 4. Overfitting Detection Summary
        overfitting_analysis = self.detect_overfitting()
        self._plot_overfitting_summary(overfitting_analysis, output_dir if save_plots else None)
        
        if save_plots:
            logger.info(f"✅ Plots saved to {output_dir}")
        
        return overfitting_analysis
    
    def _plot_kfold_performance(self, save_dir):
        """Plot K-Fold performance for each model"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        colors = ['blue', 'green', 'red', 'orange']
        
        for i, (model_name, results) in enumerate(self.results.items()):
            if i >= len(axes):
                break
                
            ax = axes[i]
            color = colors[i % len(colors)]
            
            k_values = results['k_values']
            train_means = results['train_means']
            train_stds = results['train_stds']
            val_means = results['val_means']
            val_stds = results['val_stds']
            
            # Plot train and validation curves
            ax.errorbar(k_values, train_means, yerr=train_stds, 
                       label='Training', marker='o', color=color, alpha=0.7, capsize=3)
            ax.errorbar(k_values, val_means, yerr=val_stds, 
                       label='Validation', marker='s', color=color, linestyle='--', capsize=3)
            
            ax.set_title(f'{model_name}\nK-Fold Cross-Validation Performance')
            ax.set_xlabel('Number of Folds (k)')
            ax.set_ylabel('Accuracy')
            ax.legend()
            ax.grid(True, alpha=0.3)
            ax.set_xlim(1, 21)
            
            # Highlight optimal k
            overfitting_analysis = self.detect_overfitting()
            if model_name in overfitting_analysis:
                optimal_k = overfitting_analysis[model_name]['optimal_k']
                if optimal_k in k_values:
                    optimal_idx = k_values.index(optimal_k)
                    ax.axvline(x=optimal_k, color='red', linestyle=':', alpha=0.7, 
                              label=f'Optimal k={optimal_k}')
                    ax.legend()
        
        # Hide unused subplots
        for i in range(len(self.results), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'kfold_performance.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved K-Fold performance plot")
        
        plt.show()
    
    def _plot_train_val_gap(self, save_dir):
        """Plot train-validation gap analysis"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # Plot 1: Train-Val Gap vs K
        for model_name, results in self.results.items():
            k_values = results['k_values']
            train_means = np.array(results['train_means'])
            val_means = np.array(results['val_means'])
            gaps = train_means - val_means
            
            ax1.plot(k_values, gaps, marker='o', label=model_name, linewidth=2)
        
        ax1.set_title('Train-Validation Gap vs Number of Folds')
        ax1.set_xlabel('Number of Folds (k)')
        ax1.set_ylabel('Training - Validation Accuracy')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # Add interpretation zones
        ax1.axhspan(0.05, 0.15, alpha=0.2, color='yellow', label='Moderate Overfitting')
        ax1.axhspan(0.15, 1, alpha=0.2, color='red', label='High Overfitting')
        ax1.legend()
        
        # Plot 2: Average Gap per Model
        model_names = list(self.results.keys())
        avg_gaps = []
        
        for model_name in model_names:
            results = self.results[model_name]
            train_means = np.array(results['train_means'])
            val_means = np.array(results['val_means'])
            avg_gap = (train_means - val_means).mean()
            avg_gaps.append(avg_gap)
        
        bars = ax2.bar(model_names, avg_gaps, alpha=0.7)
        ax2.set_title('Average Train-Validation Gap by Model')
        ax2.set_ylabel('Average Gap')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # Color bars based on gap size
        for bar, gap in zip(bars, avg_gaps):
            if gap > 0.15:
                bar.set_color('red')
            elif gap > 0.05:
                bar.set_color('orange')
            else:
                bar.set_color('green')
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'train_val_gap_analysis.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved train-validation gap analysis")
        
        plt.show()
    
    def _plot_variance_analysis(self, save_dir):
        """Plot variance analysis"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # Plot 1: Validation Variance vs K
        for model_name, results in self.results.items():
            k_values = results['k_values']
            val_stds = results['val_stds']
            
            ax1.plot(k_values, val_stds, marker='o', label=model_name, linewidth=2)
        
        ax1.set_title('Validation Variance vs Number of Folds')
        ax1.set_xlabel('Number of Folds (k)')
        ax1.set_ylabel('Validation Standard Deviation')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Coefficient of Variation
        for model_name, results in self.results.items():
            k_values = results['k_values']
            val_means = np.array(results['val_means'])
            val_stds = np.array(results['val_stds'])
            cv_values = val_stds / val_means  # Coefficient of variation
            
            ax2.plot(k_values, cv_values, marker='s', label=model_name, linewidth=2)
        
        ax2.set_title('Coefficient of Variation vs Number of Folds')
        ax2.set_xlabel('Number of Folds (k)')
        ax2.set_ylabel('CV (std/mean)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'variance_analysis.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved variance analysis")
        
        plt.show()
    
    def _plot_overfitting_summary(self, overfitting_analysis, save_dir):
        """Plot overfitting summary"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        model_names = list(overfitting_analysis.keys())
        
        # Plot 1: Overfitting Scores
        scores = [overfitting_analysis[name]['overfitting_score'] for name in model_names]
        bars1 = ax1.bar(model_names, scores, alpha=0.7)
        ax1.set_title('Overfitting Risk Score by Model')
        ax1.set_ylabel('Overfitting Score (0-100)')
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # Color bars based on risk level
        for bar, score in zip(bars1, scores):
            if score > 20:
                bar.set_color('red')
            elif score > 10:
                bar.set_color('orange')
            else:
                bar.set_color('green')
        
        # Plot 2: Optimal K values
        optimal_ks = [overfitting_analysis[name]['optimal_k'] for name in model_names]
        bars2 = ax2.bar(model_names, optimal_ks, alpha=0.7, color='skyblue')
        ax2.set_title('Optimal K Value by Model')
        ax2.set_ylabel('Optimal K')
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, k in zip(bars2, optimal_ks):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{k}', ha='center', va='bottom')
        
        # Plot 3: Max Train-Val Gap
        max_gaps = [overfitting_analysis[name]['max_train_val_gap'] for name in model_names]
        bars3 = ax3.bar(model_names, max_gaps, alpha=0.7)
        ax3.set_title('Maximum Train-Validation Gap')
        ax3.set_ylabel('Max Gap')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
        
        # Color bars based on gap size
        for bar, gap in zip(bars3, max_gaps):
            if gap > 0.15:
                bar.set_color('red')
            elif gap > 0.05:
                bar.set_color('orange')
            else:
                bar.set_color('green')
        
        # Plot 4: Model Comparison Radar
        # This will be a simple comparison table instead
        ax4.axis('off')
        
        # Create comparison table
        table_data = []
        for name in model_names:
            analysis = overfitting_analysis[name]
            table_data.append([
                name,
                f"{analysis['optimal_k']}",
                f"{analysis['overfitting_score']:.1f}",
                f"{analysis['max_train_val_gap']:.3f}"
            ])
        
        table = ax4.table(cellText=table_data,
                         colLabels=['Model', 'Optimal K', 'Risk Score', 'Max Gap'],
                         cellLoc='center',
                         loc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)
        ax4.set_title('Overfitting Analysis Summary', pad=20)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(save_dir / 'overfitting_summary.png', dpi=300, bbox_inches='tight')
            logger.info("✅ Saved overfitting summary")
        
        plt.show()
    
    def generate_report(self, overfitting_analysis):
        """Generate comprehensive K-Fold analysis report"""
        report = []
        report.append("="*80)
        report.append("K-FOLD CROSS-VALIDATION OVERFITTING ANALYSIS REPORT")
        report.append("="*80)
        
        # Dataset info
        report.append(f"\n📊 DATASET INFORMATION:")
        report.append(f"   • Total samples: {len(self.data)}")
        report.append(f"   • Features: {self.X.shape[1]}")
        report.append(f"   • Target distribution: {dict(self.y.value_counts())}")
        report.append(f"   • K values tested: {min(self.k_values)} to {max(self.k_values)}")
        
        # Model performance summary
        report.append(f"\n🎯 MODEL PERFORMANCE SUMMARY:")
        for model_name, results in self.results.items():
            best_val_idx = np.argmax(results['val_means'])
            best_k = results['k_values'][best_val_idx]
            best_val = results['val_means'][best_val_idx]
            best_val_std = results['val_stds'][best_val_idx]
            best_train = results['train_means'][best_val_idx]
            
            report.append(f"   • {model_name}:")
            report.append(f"     - Best validation: {best_val:.4f} ± {best_val_std:.4f} (k={best_k})")
            report.append(f"     - Training at best k: {best_train:.4f}")
            report.append(f"     - Train-val gap: {best_train - best_val:.4f}")
        
        # Overfitting analysis
        report.append(f"\n🔍 OVERFITTING ANALYSIS:")
        
        # Sort models by overfitting risk
        sorted_models = sorted(overfitting_analysis.items(), 
                             key=lambda x: x[1]['overfitting_score'])
        
        report.append("   Models ranked by overfitting risk (low to high):")
        for i, (model_name, analysis) in enumerate(sorted_models):
            risk_level = "LOW" if analysis['overfitting_score'] < 10 else \
                        "MEDIUM" if analysis['overfitting_score'] < 20 else "HIGH"
            
            report.append(f"   {i+1}. {model_name} - {risk_level} RISK")
            report.append(f"      • Overfitting score: {analysis['overfitting_score']:.2f}")
            report.append(f"      • Optimal k: {analysis['optimal_k']}")
            report.append(f"      • Max train-val gap: {analysis['max_train_val_gap']:.4f}")
        
        # Recommendations
        report.append(f"\n💡 RECOMMENDATIONS:")
        
        # Best model
        best_model = sorted_models[0]
        report.append(f"   • BEST MODEL: {best_model[0]} (lowest overfitting risk)")
        report.append(f"   • RECOMMENDED K: {best_model[1]['optimal_k']}")
        
        # General recommendations
        high_risk_models = [name for name, analysis in overfitting_analysis.items() 
                           if analysis['overfitting_score'] > 20]
        
        if high_risk_models:
            report.append(f"   • HIGH RISK MODELS: {', '.join(high_risk_models)}")
            report.append(f"   • Consider regularization or feature selection for high-risk models")
        
        # K value recommendations
        optimal_ks = [analysis['optimal_k'] for analysis in overfitting_analysis.values()]
        avg_optimal_k = np.mean(optimal_ks)
        report.append(f"   • AVERAGE OPTIMAL K: {avg_optimal_k:.1f}")
        
        if avg_optimal_k < 5:
            report.append(f"   • Low k values suggest small dataset - consider data augmentation")
        elif avg_optimal_k > 15:
            report.append(f"   • High k values indicate good dataset size for robust validation")
        
        report.append("="*80)
        
        # Save report
        report_text = '\n'.join(report)
        
        output_dir = Path("results/kfold_analysis")
        output_dir.mkdir(parents=True, exist_ok=True)
        report_file = output_dir / 'kfold_analysis_report.txt'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
        
        print(report_text)
        logger.info(f"✅ Report saved to {report_file}")
        
        return report_text


def main():
    """Main function"""
    # Check for data file
    data_path = "dataset/processed/safe_ml_fatigue_dataset.csv"
    
    # if not Path(data_path).exists():
    #     print(f"❌ Data file not found: {data_path}")
        
    #     # Look for alternatives
    #     alternatives = [
    #         "dataset/processed/safe_ml_bias_corrected_dataset.csv",
    #         "dataset/processed/safe_ml_title_only_dataset.csv",
    #         "dataset/processed/fatigue_risk_classified_dataset.csv"
    #     ]
        
    #     for alt_path in alternatives:
    #         if Path(alt_path).exists():
    #             print(f"✅ Using alternative: {alt_path}")
    #             data_path = alt_path
    #             break
    #     else:
    #         print("❌ No suitable data file found. Please run the main pipeline first.")
    #         return
    
    print("🚀 Starting K-Fold Overfitting Analysis...")
    print(f"📁 Using data: {data_path}")
    
    # Initialize analyzer
    analyzer = KFoldOverfittingAnalyzer(data_path)
    
    # Run analysis
    analyzer.analyze_kfold_performance()
    
    # Create plots and get overfitting analysis
    overfitting_analysis = analyzer.create_kfold_plots(save_plots=True)
    
    # Generate report
    analyzer.generate_report(overfitting_analysis)
    
    print("\n🎉 K-Fold overfitting analysis completed!")
    print("📁 Results saved to: results/kfold_analysis/")


if __name__ == "__main__":
    main()
