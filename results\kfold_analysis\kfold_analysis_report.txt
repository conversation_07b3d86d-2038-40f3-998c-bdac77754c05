================================================================================
K-FOLD CROSS-VALIDATION OVERFITTING ANALYSIS REPORT
================================================================================

📊 DATASET INFORMATION:
   • Total samples: 291
   • Features: 18
   • Target distribution: {'medium_risk': np.int64(145), 'high_risk': np.int64(100), 'low_risk': np.int64(46)}
   • K values tested: 2 to 20

🎯 MODEL PERFORMANCE SUMMARY:
   • Logistic Regression:
     - Best validation: 0.6391 ± 0.0322 (k=2)
     - Training at best k: 0.6977
     - Train-val gap: 0.0587
   • Random Forest:
     - Best validation: 0.9454 ± 0.0435 (k=14)
     - Training at best k: 0.9997
     - Train-val gap: 0.0543
   • Gradient Boosting:
     - Best validation: 0.9279 ± 0.0111 (k=4)
     - Training at best k: 1.0000
     - Train-val gap: 0.0721
   • XGBoost:
     - Best validation: 0.9348 ± 0.0455 (k=13)
     - Training at best k: 1.0000
     - Train-val gap: 0.0652

🔍 OVERFITTING ANALYSIS:
   Models ranked by overfitting risk (low to high):
   1. Random Forest - LOW RISK
      • Overfitting score: 5.24
      • Optimal k: 4
      • Max train-val gap: 0.0722
   2. XGBoost - LOW RISK
      • Overfitting score: 5.93
      • Optimal k: 6
      • Max train-val gap: 0.0859
   3. Gradient Boosting - LOW RISK
      • Overfitting score: 6.77
      • Optimal k: 4
      • Max train-val gap: 0.1168
   4. Logistic Regression - MEDIUM RISK
      • Overfitting score: 11.52
      • Optimal k: 2
      • Max train-val gap: 0.0917

💡 RECOMMENDATIONS:
   • BEST MODEL: Random Forest (lowest overfitting risk)
   • RECOMMENDED K: 4
   • AVERAGE OPTIMAL K: 4.0
   • Low k values suggest small dataset - consider data augmentation
================================================================================