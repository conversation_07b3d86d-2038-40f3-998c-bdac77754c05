# BAB II

# LANDASAN TEORI

## 2.1 Aktivitas Kardiovaskular

### 2.1.1 Definisi dan Konsep Dasar

Aktivitas kardiovaskular merupakan bentuk latihan fisik yang melibatkan sistem jantung dan pembuluh darah untuk meningkatkan kapasitas aerobik tubuh [16]. Aktivitas kardiovaskular didefinisikan sebagai aktivitas fisik yang meningkatkan detak jantung dan pernapasan secara berkelanjutan, melibatkan kelompok otot besar, dan dapat dipertahankan dalam periode waktu tertentu [1].

<PERSON><PERSON> konteks mahasiswa, aktivitas kardiovaskular mencakup berbagai bentuk latihan seperti berjalan kaki, berl<PERSON>, bersepeda, berenang, dan aktivitas olahraga lainnya yang dapat meningkatkan kebugaran kardiorespiratori. Aktivitas ini memiliki peran penting dalam menjaga kesehatan fisik dan mental, terutama bagi populasi mahasiswa yang cenderung memiliki gaya hidup sedentari akibat tuntutan akademik.

### 2.1.2 Parameter dan Metrik Aktivitas Kardiovaskular

Parameter utama dalam mengukur aktivitas kardiovaskular meliputi intensitas, durasi, frekuensi, dan jenis aktivitas. Intensitas aktivitas dapat diukur melalui heart rate zones, perceived exertion scale, atau metabolic equivalent of task (METs). Durasi mengacu pada lamanya aktivitas dilakukan dalam satu sesi, sedangkan frekuensi menunjukkan seberapa sering aktivitas dilakukan dalam periode tertentu.

Dalam penelitian ini, metrik aktivitas kardiovaskular yang digunakan mencakup total distance (jarak tempuh), moving time (durasi aktivitas), activity frequency (frekuensi aktivitas), dan activity type (jenis aktivitas). Data ini diperoleh melalui platform Strava yang memungkinkan tracking aktivitas secara real-time dengan akurasi tinggi menggunakan GPS dan sensor wearable devices.

### 2.1.3 Manfaat Aktivitas Kardiovaskular untuk Mahasiswa

Aktivitas kardiovaskular memberikan manfaat multidimensional bagi mahasiswa, baik dari aspek fisik, mental, maupun kognitif. Secara fisik, aktivitas kardiovaskular meningkatkan kapasitas aerobik, memperkuat sistem kardiovaskular, mengontrol berat badan, dan meningkatkan sistem imun. Manfaat mental meliputi pengurangan stres, peningkatan mood, dan pencegahan depresi dan kecemasan.

Dari aspek kognitif, penelitian menunjukkan bahwa aktivitas kardiovaskular dapat meningkatkan fungsi eksekutif, memori kerja, dan kemampuan konsentrasi yang sangat penting untuk performa akademik mahasiswa [2], [3]. Aktivitas fisik teratur juga berkaitan dengan peningkatan neuroplastisitas dan neurogenesis yang mendukung proses pembelajaran dan retensi informasi [4].

## 2.2 Produktivitas Akademik

### 2.2.1 Konsep Produktivitas dalam Konteks Akademik

Produktivitas akademik merujuk pada efisiensi dan efektivitas mahasiswa dalam menyelesaikan tugas-tugas akademik dan mencapai tujuan pembelajaran. Konsep ini mencakup kemampuan untuk mengoptimalkan penggunaan waktu, sumber daya, dan energi untuk menghasilkan output akademik yang berkualitas dalam periode waktu tertentu.

Dalam penelitian ini, produktivitas akademik diukur melalui berbagai indikator yang diperoleh dari platform Pomokit, termasuk jumlah siklus pomodoro yang diselesaikan, konsistensi dalam bekerja, efisiensi waktu, dan tingkat pencapaian target yang telah ditetapkan. Pendekatan ini memberikan gambaran objektif tentang pola kerja dan produktivitas mahasiswa dalam aktivitas akademik sehari-hari.

### 2.2.2 Teknik Pomodoro dan Manajemen Waktu

Teknik Pomodoro merupakan metode manajemen waktu yang dikembangkan oleh Francesco Cirillo pada akhir 1980-an [26]. Teknik ini melibatkan pembagian waktu kerja menjadi interval-interval fokus (biasanya 25 menit) yang dipisahkan oleh istirahat singkat. Setelah menyelesaikan beberapa interval, dilakukan istirahat yang lebih panjang.

Penelitian menunjukkan bahwa teknik Pomodoro efektif dalam meningkatkan fokus, mengurangi prokrastinasi, dan meningkatkan produktivitas akademik mahasiswa [26]. Dalam konteks mahasiswa, teknik ini membantu dalam mengelola beban kerja akademik yang berat dan mempertahankan konsentrasi dalam periode belajar yang panjang. Platform Pomokit mengimplementasikan teknik ini dengan fitur tracking dan gamifikasi untuk meningkatkan motivasi pengguna [9].

### 2.2.3 Faktor-faktor yang Mempengaruhi Produktivitas Akademik

Produktivitas akademik dipengaruhi oleh berbagai faktor internal dan eksternal. Faktor internal meliputi motivasi, self-efficacy, kemampuan manajemen waktu, tingkat stres, dan kondisi kesehatan fisik dan mental. Faktor eksternal mencakup lingkungan belajar, dukungan sosial, teknologi yang tersedia, dan beban kerja akademik.

Dalam penelitian ini, fokus diberikan pada klasifikasi risiko fatigue berdasarkan pola aktivitas fisik dan produktivitas akademik mahasiswa. Pendekatan ini memungkinkan identifikasi dini mahasiswa yang berisiko mengalami fatigue sehingga dapat dilakukan intervensi preventif yang tepat waktu.

![Kerangka Teoritis Klasifikasi Fatigue](laporan-akhir/figures/gambar_2_1_kerangka_teoritis.png)

**Gambar 2.1** Kerangka Teoritis Klasifikasi Fatigue pada Mahasiswa

## 2.3 Fatigue dan Klasifikasi Risiko

### 2.3.1 Definisi dan Jenis Fatigue

Fatigue atau kelelahan merupakan kondisi subjektif yang ditandai dengan perasaan lelah, kurang energi, dan penurunan kapasitas untuk melakukan aktivitas fisik atau mental. Dalam konteks akademik, fatigue dapat mempengaruhi kemampuan belajar, konsentrasi, dan performa akademik secara keseluruhan.

Fatigue dapat diklasifikasikan menjadi beberapa jenis: physical fatigue (kelelahan fisik), mental fatigue (kelelahan mental), dan emotional fatigue (kelelahan emosional). Physical fatigue berkaitan dengan kelelahan otot dan sistem kardiovaskular, mental fatigue terkait dengan penurunan fungsi kognitif dan konsentrasi, sedangkan emotional fatigue berkaitan dengan stres dan beban emosional.

### 2.3.2 Faktor Risiko dan Indikator Fatigue

Faktor risiko fatigue pada mahasiswa meliputi beban kerja akademik yang berlebihan, kurang tidur, pola makan yang tidak sehat, kurangnya aktivitas fisik, stres akademik, dan ketidakseimbangan work-life balance [11], [29]. Indikator fatigue dapat diidentifikasi melalui berbagai parameter seperti penurunan produktivitas, kesulitan konsentrasi, perubahan mood, dan penurunan motivasi [27], [30].

Dalam penelitian ini, klasifikasi risiko fatigue dilakukan berdasarkan kombinasi indikator dari aktivitas fisik (data Strava) dan produktivitas akademik (data Pomokit) [5]. Model klasifikasi menggunakan machine learning untuk mengidentifikasi pola yang berkaitan dengan risiko fatigue rendah, sedang, dan tinggi berdasarkan behavioral patterns mahasiswa [18].

Klasifikasi risiko fatigue dibagi menjadi tiga kategori:

-   **Low Risk**: Mahasiswa dengan pola aktivitas fisik teratur dan produktivitas akademik yang konsisten
-   **Medium Risk**: Mahasiswa dengan fluktuasi dalam aktivitas fisik atau produktivitas yang memerlukan monitoring
-   **High Risk**: Mahasiswa dengan pola aktivitas yang tidak konsisten dan produktivitas rendah yang berisiko tinggi mengalami fatigue

### 2.3.3 Dampak Fatigue terhadap Performa Akademik

Fatigue memiliki dampak signifikan terhadap performa akademik mahasiswa. Penelitian menunjukkan bahwa mahasiswa yang mengalami fatigue cenderung memiliki nilai akademik yang lebih rendah, tingkat absensi yang lebih tinggi, dan kesulitan dalam menyelesaikan tugas-tugas akademik tepat waktu.

Dampak fatigue juga meluas ke aspek kesehatan mental, termasuk peningkatan risiko depresi, kecemasan, dan burnout akademik [4]. Oleh karena itu, identifikasi dini dan manajemen fatigue menjadi krusial untuk mempertahankan kesehatan dan performa akademik mahasiswa [11].

## 2.4 Machine Learning dalam Healthcare

### 2.4.1 Penerapan Machine Learning untuk Prediksi Kesehatan

Machine learning telah menjadi tool yang powerful dalam bidang healthcare untuk prediksi, diagnosis, dan personalisasi treatment [19], [20]. Dalam konteks health monitoring, algoritma machine learning dapat menganalisis pola data kesehatan untuk mengidentifikasi risiko penyakit, memprediksi outcome, dan memberikan rekomendasi preventif [15].

Penerapan machine learning dalam healthcare mencakup berbagai domain seperti medical imaging, drug discovery, electronic health records analysis, dan wearable device data analysis. Dalam penelitian ini, fokus diberikan pada penggunaan machine learning untuk menganalisis data aktivitas fisik dan produktivitas untuk prediksi risiko fatigue.

### 2.4.2 Algoritma Klasifikasi untuk Fatigue Prediction

Algoritma klasifikasi merupakan subset dari supervised learning yang bertujuan untuk mengkategorikan data ke dalam kelas-kelas tertentu. Dalam konteks fatigue prediction, algoritma klasifikasi digunakan untuk mengklasifikasikan mahasiswa ke dalam kategori risiko fatigue (low, medium, high) berdasarkan pola aktivitas fisik dan produktivitas akademik.

**Random Forest** dipilih karena kemampuannya menangani mixed data types dan memberikan feature importance yang interpretable. Algoritma ini robust terhadap outliers dan cocok untuk dataset dengan ukuran medium seperti dalam penelitian ini [35].

**Support Vector Machine (SVM)** efektif untuk high-dimensional data dan dapat menangkap non-linear patterns melalui kernel trick. SVM dengan RBF kernel cocok untuk menangani complex relationships dalam behavioral data [39].

**Neural Networks** memiliki kemampuan universal approximation dan dapat mempelajari complex patterns dalam data. Dengan penambahan SMOTE untuk mengatasi class imbalance, Neural Networks memberikan performa terbaik dalam penelitian ini [32], [33].

### 2.4.3 Feature Engineering untuk Fatigue Classification

Feature engineering dalam konteks fatigue classification melibatkan transformasi data aktivitas fisik dan produktivitas menjadi features yang dapat digunakan untuk klasifikasi risiko fatigue [22]. Features yang digunakan dalam penelitian ini meliputi:

**Physical Activity Features**: total_distance_km, avg_distance_km, activity_days, total_time_minutes, avg_time_minutes yang diekstrak dari data Strava untuk menggambarkan pola aktivitas kardiovaskular mahasiswa.

**Productivity Features**: total_cycles, avg_cycles, work_days, consistency_score, productivity_points, achievement_rate yang diperoleh dari platform Pomokit untuk mengukur produktivitas akademik.

**Gamification Features**: activity_points, gamification_balance yang menggambarkan tingkat engagement mahasiswa dalam aktivitas fisik dan akademik.

**Text-based Features**: Features yang diekstrak dari judul aktivitas menggunakan natural language processing untuk menangkap aspek kualitatif dari behavioral patterns mahasiswa.

Feature selection dilakukan melalui systematic ablation study untuk mengidentifikasi features yang paling kontributif terhadap akurasi klasifikasi fatigue [32], [33].

## 2.5 Ablation Study dalam Machine Learning

### 2.5.1 Konsep dan Tujuan Ablation Study

Ablation study merupakan metodologi penelitian yang bertujuan untuk memahami kontribusi individual features dalam model klasifikasi fatigue melalui systematic removal features tersebut. Dalam penelitian ini, ablation study dilakukan dengan menghilangkan satu feature pada satu waktu untuk mengevaluasi dampaknya terhadap akurasi klasifikasi.

Tujuan utama ablation study dalam penelitian ini adalah untuk mengidentifikasi features yang paling kontributif terhadap klasifikasi fatigue, memvalidasi robustness model, dan menentukan minimal feature set yang diperlukan untuk mempertahankan performa klasifikasi yang baik. Metodologi ini penting untuk memastikan bahwa model klasifikasi fatigue tidak hanya akurat tetapi juga praktis untuk implementasi.

### 2.5.2 Metodologi Systematic Feature Removal

Systematic feature removal melibatkan proses penghilangan features secara terstruktur untuk mengevaluasi dampaknya terhadap performa model. Pendekatan ini dapat dilakukan secara individual (menghilangkan satu feature pada satu waktu) atau secara kombinatorial (menghilangkan kombinasi features).

Dalam penelitian ini, ablation study dilakukan dengan mengevaluasi performa model klasifikasi fatigue ketika features tertentu dihilangkan dari training set. Hasil ablation study memberikan insight tentang feature importance untuk klasifikasi fatigue, redundancy antar features, dan optimal feature set yang diperlukan untuk mempertahankan akurasi klasifikasi yang baik.

### 2.5.3 Evaluasi dan Interpretasi Hasil Ablation Study

Evaluasi hasil ablation study melibatkan analisis perubahan akurasi klasifikasi fatigue ketika features dihilangkan [22]. Metrics yang digunakan meliputi accuracy, precision, recall, dan F1-score untuk setiap kelas fatigue (low, medium, high risk) [23]. Features yang menyebabkan penurunan akurasi klasifikasi signifikan ketika dihilangkan dianggap sebagai features yang penting untuk prediksi fatigue [24].

Interpretasi hasil ablation study juga melibatkan analisis kontribusi relatif setiap feature terhadap klasifikasi fatigue dan identification of minimal feature set yang masih dapat memberikan akurasi klasifikasi yang acceptable. Hal ini penting untuk pengembangan model klasifikasi fatigue yang efisien dan praktis untuk implementasi dalam monitoring kesehatan mahasiswa.

## 2.6 Gamifikasi dalam Aplikasi Kesehatan

### 2.6.1 Konsep Gamifikasi dan Motivasi

Gamifikasi merupakan penerapan elemen-elemen game design dalam konteks non-game untuk meningkatkan engagement, motivasi, dan behavior change. Dalam aplikasi kesehatan dan produktivitas, gamifikasi dapat meningkatkan adherence terhadap program kesehatan dan mempertahankan motivasi jangka panjang.

Elemen gamifikasi yang umum digunakan meliputi points, badges, leaderboards, progress bars, challenges, dan rewards. Dalam penelitian ini, analisis dilakukan terhadap dampak elemen gamifikasi dalam aplikasi Pomokit terhadap konsistensi aktivitas dan risiko fatigue mahasiswa.

### 2.6.2 Psychological Theories dalam Gamifikasi

Efektivitas gamifikasi didukung oleh berbagai teori psikologi, termasuk Self-Determination Theory, Flow Theory, dan Social Cognitive Theory [9]. Self-Determination Theory menekankan pentingnya autonomy, competence, dan relatedness dalam mempertahankan motivasi intrinsik [10].

Flow Theory menjelaskan kondisi optimal dimana individu mengalami engagement penuh dalam aktivitas, yang dapat difasilitasi melalui design gamifikasi yang appropriate. Social Cognitive Theory menekankan peran self-efficacy dan social learning dalam behavior change yang dapat diperkuat melalui elemen gamifikasi.

### 2.6.3 Efektivitas Gamifikasi dalam Health Behavior Change

Penelitian menunjukkan bahwa gamifikasi dapat efektif dalam mendorong health behavior change, termasuk peningkatan aktivitas fisik, adherence terhadap medication, dan lifestyle modification [9], [10]. Namun, efektivitas gamifikasi tergantung pada design yang appropriate, target population, dan sustainability jangka panjang.

Dalam konteks mahasiswa, gamifikasi dapat membantu dalam mempertahankan motivasi untuk aktivitas fisik dan produktivitas akademik. Penelitian ini menganalisis hubungan antara achievement rate, gamification balance, dan risiko fatigue untuk memahami optimal threshold gamifikasi yang dapat mempertahankan engagement tanpa menyebabkan burnout.

## 2.7 Text-Based Feature Engineering untuk Health Analytics

### 2.7.1 Konsep Text Mining dalam Analisis Data Kesehatan

Text mining merupakan proses ekstraksi informasi dari data tekstual untuk mengidentifikasi pola dan insights yang berguna dalam analisis kesehatan [29]. Dalam konteks health analytics, text mining dapat digunakan untuk menganalisis deskripsi aktivitas, user-generated content, dan behavioral descriptions untuk mengekstrak indikator kesehatan yang valuable [28], [31].

Pendekatan text mining dalam penelitian ini fokus pada analisis judul aktivitas (activity titles) dari platform Strava dan Pomokit untuk mengidentifikasi indikator fatigue berdasarkan keyword patterns dan linguistic characteristics. Metode ini menggunakan rule-based approach dengan predefined dictionaries untuk menangkap behavioral indicators yang terekam dalam activity descriptions.

### 2.7.2 Keyword-Based Feature Extraction untuk Fatigue Detection

Keyword-based feature extraction merupakan pendekatan rule-based yang mengidentifikasi specific terms atau phrases yang berkaitan dengan kondisi fatigue [29]. Pendekatan ini efektif untuk menangkap explicit indicators dalam activity descriptions yang mungkin tidak tertangkap oleh quantitative metrics saja [27], [30].

Dalam penelitian ini, keyword-based extraction diterapkan pada judul aktivitas untuk mengidentifikasi patterns yang berkaitan dengan stress, workload, negative emotions, recovery, dan time pressure. Dictionary-based approach digunakan dengan keywords yang telah didefinisikan berdasarkan domain knowledge dan literature review untuk mengidentifikasi indikator seperti stress count, workload count, negative emotion count, dan recovery count.

### 2.7.3 Linguistic Feature Engineering dari Activity Titles

Linguistic feature engineering melibatkan ekstraksi statistical dan structural features dari text data untuk menangkap characteristics yang relevan dengan fatigue prediction. Features ini meliputi text length metrics, word frequency measures, dan emotional intensity indicators yang dapat diekstrak tanpa memerlukan complex natural language processing.

Pendekatan yang digunakan menggabungkan keyword counting dengan linguistic statistics seperti total words, unique words, title length, dan emotional indicators (exclamation marks, capitalization ratio). Kombinasi features ini memberikan representasi comprehensive dari behavioral patterns yang terekam dalam activity descriptions. Title-only analysis dikembangkan sebagai pendekatan alternatif yang praktis dan dapat diimplementasikan dengan mudah tanpa memerlukan sensor atau tracking device yang kompleks.

## 2.8 Integrasi Data Multi-Platform

### 2.8.1 Konsep Data Integration dalam Health Monitoring

Data integration merupakan proses penggabungan data dari berbagai sumber untuk memberikan pandangan yang unified dan comprehensive [34]. Dalam health monitoring, integrasi data dari multiple platforms dapat memberikan insight yang lebih holistik tentang kondisi kesehatan dan behavior patterns individu [40].

Tantangan dalam data integration meliputi data heterogeneity, quality issues, privacy concerns, dan technical compatibility [37]. Dalam penelitian ini, integrasi dilakukan antara data aktivitas fisik dari Strava dan data produktivitas dari Pomokit untuk menganalisis hubungan antara kedua aspek tersebut dalam konteks fatigue prediction [40].

### 2.8.2 Preprocessing dan Harmonisasi Data

Preprocessing merupakan tahap krusial dalam data integration yang melibatkan cleaning, transformation, dan harmonization data dari berbagai sumber. Proses ini mencakup handling missing values, data type conversion, temporal alignment, dan feature standardization untuk memastikan kompatibilitas data.

Dalam penelitian ini, preprocessing dilakukan untuk menyelaraskan data temporal dari kedua platform, menangani missing values, dan menciptakan features yang konsisten untuk analisis. Proses harmonisasi juga melibatkan creation of derived features yang dapat menangkap interaksi antara aktivitas fisik dan produktivitas.

### 2.8.3 Keunggulan dan Tantangan Multi-Platform Analysis

Multi-platform analysis memberikan keunggulan dalam hal comprehensiveness, validation, dan robustness hasil penelitian. Dengan mengintegrasikan data dari berbagai sumber, penelitian dapat menangkap aspek yang lebih luas dari behavior dan health patterns yang mungkin tidak terdeteksi dari single platform.

Namun, multi-platform analysis juga menghadapi tantangan seperti data complexity, increased computational requirements, dan potential for data leakage. Dalam penelitian ini, teknik feature filtering dan bias correction diterapkan untuk memastikan validity dan reliability hasil analisis multi-platform.

## 2.9 Kerangka Teoritis Penelitian

### 2.9.1 Model Hubungan Aktivitas Fisik dan Produktivitas

Berdasarkan tinjauan literatur, dikembangkan kerangka teoritis yang menjelaskan hubungan antara aktivitas kardiovaskular, produktivitas akademik, dan risiko fatigue. Model ini mengintegrasikan teori-teori dari exercise physiology, cognitive psychology, dan health behavior untuk menjelaskan mekanisme underlying yang menghubungkan ketiga variabel tersebut.

Model teoritis menunjukkan bahwa aktivitas kardiovaskular dapat meningkatkan produktivitas akademik melalui peningkatan fungsi kognitif, pengurangan stres, dan peningkatan mood. Sebaliknya, ketidakseimbangan antara aktivitas fisik dan beban akademik dapat meningkatkan risiko fatigue yang berdampak negatif pada kedua aspek tersebut.

![Kerangka Teoritis](laporan-akhir/figures/gambar_2_1_kerangka_teoritis.png)

**Gambar 2.1** Kerangka Teoritis Hubungan Aktivitas Fisik, Produktivitas, dan Fatigue

### 2.9.2 Hipotesis Penelitian

Berdasarkan kerangka teoritis yang dikembangkan, dirumuskan beberapa hipotesis penelitian:

1. **Hipotesis 1** - Terdapat korelasi positif yang signifikan antara tingkat aktivitas kardiovaskular dan produktivitas akademik mahasiswa.

2. **Hipotesis 2** - Model machine learning dapat mengklasifikasikan risiko fatigue dengan akurasi yang tinggi berdasarkan data aktivitas kardiovaskular dan produktivitas akademik.

3. **Hipotesis 3** - Title-only analysis dapat memberikan prediksi fatigue yang akurat sebagai alternatif dari model berbasis data kuantitatif lengkap.

4. **Hipotesis 4** - Elemen gamifikasi memiliki hubungan yang signifikan dengan konsistensi aktivitas dan risiko fatigue mahasiswa.

5. **Hipotesis 5** - Systematic ablation study akan mengidentifikasi subset features yang optimal untuk prediksi fatigue tanpa mengorbankan akurasi model.

### 2.9.3 Kontribusi Teoritis yang Diharapkan

Penelitian ini diharapkan memberikan kontribusi teoritis dalam beberapa aspek. Pertama, pengembangan model integratif yang menjelaskan hubungan kompleks antara aktivitas fisik, produktivitas akademik, dan fatigue dalam konteks mahasiswa. Kedua, validasi empiris terhadap efektivitas machine learning untuk health prediction menggunakan data behavioral.

Ketiga, pengembangan metodologi ablation study yang rigorous untuk health prediction models yang dapat menjadi referensi untuk penelitian serupa. Keempat, eksplorasi potensi NLP untuk health analytics yang dapat membuka avenue baru dalam digital health monitoring. Kelima, pemahaman yang lebih mendalam tentang peran gamifikasi dalam health behavior change dan sustainability.
