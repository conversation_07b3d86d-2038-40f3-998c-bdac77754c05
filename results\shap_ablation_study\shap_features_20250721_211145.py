"""
SHAP-Based Optimal Features - Generated 20250721_211145
Best Algorithm: Random Forest (Accuracy: 0.9322)
"""

# All features ranked by SHAP importance
SHAP_RANKED_FEATURES = [
    "productivity_points",  # SHAP: 0.2126
    "strava_title_count",  # SHAP: 0.1120
    "gamification_balance",  # SHAP: 0.0299
    "achievement_rate",  # SHAP: 0.0269
    "total_distance_km",  # SHAP: 0.0223
    "title_balance_ratio",  # SHAP: 0.0198
    "pomokit_title_length",  # SHAP: 0.0157
    "strava_title_length",  # SHAP: 0.0123
    "activity_points",  # SHAP: 0.0079
    "strava_unique_words",  # SHAP: 0.0074
    "pomokit_title_count",  # SHAP: 0.0060
    "total_title_diversity",  # SHAP: 0.0054
    "total_time_minutes",  # SHAP: 0.0052
    "avg_time_minutes",  # SHAP: 0.0043
    "avg_distance_km",  # SHAP: 0.0035
    "pomokit_unique_words",  # SHAP: 0.0013
    "avg_cycles",  # SHAP: 0.0000
    "weekly_efficiency",  # SHAP: 0.0000
]

# Top 5 SHAP features
TOP_5_SHAP_FEATURES = [
    "productivity_points",  # SHAP: 0.2126
    "strava_title_count",  # SHAP: 0.1120
    "gamification_balance",  # SHAP: 0.0299
    "achievement_rate",  # SHAP: 0.0269
    "total_distance_km",  # SHAP: 0.0223
]

# Top 10 SHAP features
TOP_10_SHAP_FEATURES = [
    "productivity_points",  # SHAP: 0.2126
    "strava_title_count",  # SHAP: 0.1120
    "gamification_balance",  # SHAP: 0.0299
    "achievement_rate",  # SHAP: 0.0269
    "total_distance_km",  # SHAP: 0.0223
    "title_balance_ratio",  # SHAP: 0.0198
    "pomokit_title_length",  # SHAP: 0.0157
    "strava_title_length",  # SHAP: 0.0123
    "activity_points",  # SHAP: 0.0079
    "strava_unique_words",  # SHAP: 0.0074
]

# Top 15 SHAP features
TOP_15_SHAP_FEATURES = [
    "productivity_points",  # SHAP: 0.2126
    "strava_title_count",  # SHAP: 0.1120
    "gamification_balance",  # SHAP: 0.0299
    "achievement_rate",  # SHAP: 0.0269
    "total_distance_km",  # SHAP: 0.0223
    "title_balance_ratio",  # SHAP: 0.0198
    "pomokit_title_length",  # SHAP: 0.0157
    "strava_title_length",  # SHAP: 0.0123
    "activity_points",  # SHAP: 0.0079
    "strava_unique_words",  # SHAP: 0.0074
    "pomokit_title_count",  # SHAP: 0.0060
    "total_title_diversity",  # SHAP: 0.0054
    "total_time_minutes",  # SHAP: 0.0052
    "avg_time_minutes",  # SHAP: 0.0043
    "avg_distance_km",  # SHAP: 0.0035
]

# SHAP importance scores
SHAP_IMPORTANCE_SCORES = {
    "productivity_points": 0.212582,
    "strava_title_count": 0.112018,
    "gamification_balance": 0.029938,
    "achievement_rate": 0.026874,
    "total_distance_km": 0.022348,
    "title_balance_ratio": 0.019789,
    "pomokit_title_length": 0.015721,
    "strava_title_length": 0.012323,
    "activity_points": 0.007878,
    "strava_unique_words": 0.007393,
    "pomokit_title_count": 0.005955,
    "total_title_diversity": 0.005384,
    "total_time_minutes": 0.005229,
    "avg_time_minutes": 0.004281,
    "avg_distance_km": 0.003474,
    "pomokit_unique_words": 0.001325,
    "avg_cycles": 0.000000,
    "weekly_efficiency": 0.000000,
}

# Usage example:
# X_shap = X[TOP_5_SHAP_FEATURES]
# model.fit(X_shap, y)