# BAB III

# METODOLOGI PENELITIAN

## 3.1 Desain Penelitian

Penelitian ini menggunakan **desain cross-sectional dengan elemen longitudinal terbatas** untuk menganalisis hubungan antara aktivitas kardiovaskular dan produktivitas akademik dalam prediksi risiko fatigue pada mahasiswa. Pendekatan **observational study** dipilih untuk menangkap pola perilaku natural mahasiswa tanpa intervensi yang dapat mempengaruhi validitas data.

Penelitian ini menerapkan **mixed-methods approach** yang mengkombinasikan:

-   **Quantitative analysis** untuk data aktivitas fisik dan produktivitas
-   **Text-based analysis** untuk analisis judul aktivitas
-   **Machine learning modeling** untuk prediksi risiko fatigue
-   **Ablation study methodology** untuk validasi feature importance

## 3.2 Alur Metodologi Penelitian

Berdasarkan diagram alur metodologi yang telah disusun, penelitian ini mengikuti tahapan sistematis sebagai berikut:

![Diagram Alur Metodologi](diagram%20alur%20metodologi.png)

### 3.2.1 Tahapan Utama Penelitian

**1. Data Collection** → **2. Data Preprocessing** → **3. Feature Engineering** → **4. Labeling Strategy** → **5. Feature Selection** → **6. Training Model** → **7. Evaluation Model** → **8. Results & Analysis**

### 3.2.2 Deskripsi Setiap Tahapan

#### **Tahap 1: Data Collection**

Pada tahap pengumpulan data, penelitian ini berfokus pada populasi target yang terdiri dari mahasiswa Indonesia yang aktif menggunakan teknologi digital untuk monitoring aktivitas fisik dan produktivitas akademik. Total partisipan dalam penelitian ini berjumlah 106 mahasiswa dengan 300 observasi mingguan, di mana unit analisis yang digunakan adalah data agregasi mingguan per mahasiswa.

Kriteria inklusi dalam penelitian ini meliputi status sebagai mahasiswa aktif di perguruan tinggi Indonesia, pengguna aktif platform Strava untuk tracking aktivitas kardiovaskular, pengguna aktif aplikasi Pomokit untuk manajemen produktivitas, memiliki data aktivitas minimal 4 minggu berturut-turut, serta memiliki literasi teknologi yang memadai untuk penggunaan aplikasi tracking. Sementara itu, kriteria eksklusi mencakup data aktivitas yang tidak lengkap atau tidak konsisten, pengguna dengan kondisi medis yang mempengaruhi aktivitas fisik, dan data dengan indikasi manipulasi atau tidak valid.

Data aktivitas kardiovaskular diperoleh dari platform Strava yang mencakup jenis data berupa jarak tempuh aktivitas (km), durasi aktivitas (menit), frekuensi aktivitas per minggu, judul/deskripsi aktivitas, serta tanggal dan waktu aktivitas. Dari data tersebut, diekstrak berbagai metrik seperti total jarak tempuh mingguan (`total_distance_km`), rata-rata jarak per aktivitas (`avg_distance_km`), total durasi aktivitas mingguan (`total_time_minutes`), rata-rata durasi per aktivitas (`avg_time_minutes`), jumlah hari aktif per minggu (`activity_days`), jumlah aktivitas dengan judul (`strava_title_count`), rata-rata panjang judul aktivitas (`strava_title_length`), dan keragaman kata dalam judul (`strava_unique_words`).

Data produktivitas akademik diperoleh dari platform Pomokit yang mencakup jenis data berupa siklus pomodoro yang diselesaikan, durasi sesi kerja, judul/deskripsi tugas, tanggal dan waktu sesi kerja, serta achievement rate dan gamification metrics. Metrik yang diekstrak dari data ini meliputi total siklus pomodoro mingguan (`total_cycles`), jumlah hari kerja per minggu (`work_days`), poin produktivitas yang diperoleh (`productivity_points`), jumlah sesi dengan judul (`pomokit_title_count`), rata-rata panjang judul tugas (`pomokit_title_length`), keragaman kata dalam judul tugas (`pomokit_unique_words`), tingkat pencapaian target (`achievement_rate`), dan keseimbangan elemen gamifikasi (`gamification_balance`).

Proses pengumpulan data dilakukan melalui implementasi fungsi `load_raw_data()` yang memuat data Strava dan Pomokit dari file CSV yang tersimpan dalam direktori dataset/raw, kemudian mengembalikan kedua dataset tersebut untuk diproses lebih lanjut dalam pipeline analisis.

```python
def load_raw_data(self):
    # Load Strava data
    strava_data = pd.read_csv('dataset/raw/strava.csv')

    # Load Pomokit data
    pomokit_data = pd.read_csv('dataset/raw/pomokit.csv')

    return strava_data, pomokit_data
```

Dalam aspek etis dan privasi, penelitian ini menerapkan anonymization terhadap User ID menggunakan hash functions, melakukan removal terhadap personally identifiable information, menggunakan aggregated data analysis untuk melindungi privasi individual, menerapkan secure data storage dengan enkripsi, mengimplementasikan access control untuk data sensitif, serta memastikan compliance dengan regulasi privasi data yang berlaku.

#### **Tahap 2: Data Preprocessing**

Tahap preprocessing data melibatkan proses pembersihan data dari kedua platform. Untuk data Strava, dilakukan pembersihan pada data jarak dengan menghapus satuan 'km' dan mengganti koma dengan titik, serta mengkonversi waktu aktivitas ke dalam format menit. Selain itu, data tanggal dikonversi ke format datetime dan baris dengan tanggal yang kosong dihapus untuk memastikan integritas data. Implementasi pembersihan data Strava dilakukan dengan kode berikut:

```python
# Pembersihan data jarak dan waktu
df['distance'] = df['distance'].str.replace(' km', '').str.replace(',', '.')
df['moving_time'] = convert_time_to_minutes(df['moving_time'])

# Pembersihan data tanggal
df['date'] = pd.to_datetime(df['date'], errors='coerce')
df = df.dropna(subset=['date'])
```

Untuk data Pomokit, dilakukan konversi kolom siklus ke tipe numerik dan kolom tanggal ke format datetime, serta penghapusan baris dengan tanggal yang kosong. Implementasi pembersihan data Pomokit dilakukan dengan kode berikut:

```python
# Pembersihan data siklus dan tanggal
df['cycle'] = pd.to_numeric(df['cycle'], errors='coerce')
df['date'] = pd.to_datetime(df['date'], errors='coerce')
df = df.dropna(subset=['date'])
```

Setelah proses pembersihan, data dari kedua platform diagregasi menjadi data mingguan untuk setiap partisipan melalui fungsi `create_weekly_aggregation()`. Fungsi ini melakukan agregasi data Strava per minggu dengan menghitung jumlah, rata-rata, dan hitungan jarak, serta jumlah dan rata-rata waktu aktivitas, dan jumlah serta panjang judul aktivitas. Untuk data Pomokit, dilakukan agregasi jumlah siklus, hitungan siklus, serta jumlah dan panjang judul tugas. Kedua dataset yang telah diagregasi kemudian digabungkan berdasarkan user_id dan year_week untuk membentuk dataset mingguan yang komprehensif. Implementasi proses agregasi mingguan adalah sebagai berikut:

```python
def create_weekly_aggregation(strava_data, pomokit_data):
    # Agregasi data Strava per minggu
    strava_weekly = strava_data.groupby(['user_id', 'year_week']).agg({
        'distance': ['sum', 'mean', 'count'],
        'moving_time': ['sum', 'mean'],
        'title': ['count', lambda x: len(' '.join(x.astype(str)).split())]
    })

    # Agregasi data Pomokit per minggu
    pomokit_weekly = pomokit_data.groupby(['user_id', 'year_week']).agg({
        'cycle': ['sum', 'count'],
        'title': ['count', lambda x: len(' '.join(x.astype(str)).split())]
    })

    # Merge kedua dataset
    weekly_data = pd.merge(strava_weekly, pomokit_weekly,
                          on=['user_id', 'year_week'], how='outer')
    return weekly_data
```

#### **Tahap 3: Feature Engineering**

Tahap feature engineering melibatkan pembuatan fitur-fitur turunan yang relevan untuk prediksi risiko fatigue. Fitur pertama yang dibuat adalah Consistency Score yang menggambarkan konsistensi aktivitas fisik dan produktivitas mahasiswa, dihitung dengan mengalikan rasio hari aktif fisik terhadap total hari dalam seminggu dengan rasio hari kerja terhadap total hari dalam seminggu. Formula yang digunakan adalah `consistency_score = (activity_days / 7) * (work_days / 7)`.

Fitur kedua adalah Gamification Balance yang merepresentasikan keseimbangan antara poin aktivitas fisik dan poin produktivitas, dihitung dengan menjumlahkan activity_points dan productivity_points menggunakan formula `gamification_balance = activity_points + productivity_points`.

Fitur ketiga adalah Title Balance Ratio yang mengukur proporsi aktivitas produktivitas terhadap total aktivitas yang memiliki judul, dihitung dengan formula `title_balance_ratio = pomokit_title_count / (strava_title_count + pomokit_title_count)`.

Fitur keempat adalah Total Title Diversity yang mengukur keragaman kata dalam judul aktivitas dari kedua platform, dihitung dengan menjumlahkan unique words dari Strava dan Pomokit menggunakan formula `total_title_diversity = strava_unique_words + pomokit_unique_words`.

Proses feature engineering diimplementasikan melalui fungsi `_calculate_derived_metrics()` yang menghitung consistency score berdasarkan konsistensi fisik dan kerja yang dinormalisasi, serta menghitung weekly efficiency sebagai rasio total siklus terhadap hari kerja. Implementasi lengkap proses feature engineering adalah sebagai berikut:

```python
def _calculate_derived_metrics(self, data: pd.DataFrame) -> pd.DataFrame:
    df = data.copy()

    # Calculate consistency score
    physical_consistency = (df['activity_days'].clip(upper=2)) / 2
    work_consistency = (df['work_days'].clip(upper=5)) / 5
    df['consistency_score'] = (physical_consistency + work_consistency) / 2

    # Calculate efficiency metrics
    df['weekly_efficiency'] = np.where(
        df['work_days'] == 0,
        np.nan,
        df['total_cycles'] / df['work_days']
    )

    return df
```

#### **Tahap 4: Labeling Strategy**

Tahap labeling strategy melibatkan perhitungan skor risiko fatigue menggunakan formula multi-dimensional yang mempertimbangkan berbagai faktor workload dan recovery. Skor fatigue dihitung dengan memberikan bobot 40% untuk faktor workload yang meliputi rasio hari kerja per minggu (20 poin), intensitas kerja yang dinormalisasi (15 poin), ketidakseimbangan work-life (12 poin), defisit aktivitas (3 poin), dan defisit konsistensi (25 poin). Faktor recovery diberikan bobot 10% dengan kontribusi negatif yang meliputi jumlah recovery (8 poin) dan rasio hari aktif fisik (8 poin). Skor akhir dinormalisasi ke skala 0-100 untuk memudahkan interpretasi.

```python
def calculate_fatigue_risk_score(df):
    fatigue_score = (
        # Workload factors (40% weight)
        (df['work_days'] / 7) * 20 +
        (df['work_intensity'].clip(upper=5) / 5) * 15 +
        (df['work_life_imbalance'].clip(upper=10) / 10) * 12 +
        df['activity_deficit'] * 3 +
        df['consistency_deficit'] * 25 +

        # Recovery factors (10% weight - negative contribution)
        - df['recovery_count'] * 8 -
        - (df['activity_days'] / 7) * 8
    )

    # Normalize to 0-100 scale
    df['fatigue_risk_score'] = np.clip(fatigue_score, 0, 100)
    return df
```

Klasifikasi kategori risiko dilakukan berdasarkan skor fatigue yang telah dihitung, di mana skor ≤ 30 dikategorikan sebagai low_risk, skor 31-60 sebagai medium_risk, dan skor > 60 sebagai high_risk. Implementasi klasifikasi ini menggunakan fungsi conditional yang sederhana namun efektif.

```python
def classify_fatigue_risk(score):
    if score <= 30:
        return 'low_risk'
    elif score <= 60:
        return 'medium_risk'
    else:
        return 'high_risk'
```

Distribusi kategori dalam dataset menunjukkan bahwa 46 mahasiswa (15.8%) termasuk dalam kategori Low Risk dengan skor ≤ 30, 145 mahasiswa (49.8%) termasuk dalam kategori Medium Risk dengan skor 31-60, dan 100 mahasiswa (34.4%) termasuk dalam kategori High Risk dengan skor > 60. Distribusi ini menunjukkan bahwa mayoritas mahasiswa berada dalam kategori risiko sedang, yang mengindikasikan perlunya perhatian terhadap manajemen fatigue dalam populasi mahasiswa.

#### **Tahap 5: Feature Selection**

Tahap feature selection menggunakan beberapa metode untuk mengidentifikasi fitur-fitur yang paling berpengaruh dalam prediksi risiko fatigue. Metode pertama adalah SHAP (SHapley Additive exPlanations) yang digunakan untuk mengidentifikasi feature importance berdasarkan kontribusi masing-masing fitur terhadap prediksi model. Implementasi SHAP dilakukan melalui fungsi `calculate_shap_importance()` yang membuat explainer dari model, menghitung SHAP values, dan mengembalikan dictionary berisi feature importance berdasarkan nilai absolut rata-rata SHAP.

```python
def calculate_shap_importance(model, X, feature_names):
    explainer = shap.Explainer(model)
    shap_values = explainer(X)

    # Calculate mean absolute SHAP values
    shap_importance = np.abs(shap_values.values).mean(axis=0)

    return dict(zip(feature_names, shap_importance))
```

Metode kedua adalah Recursive Feature Elimination (RFE) yang melakukan eliminasi fitur secara rekursif berdasarkan importance ranking. Implementasi RFE dilakukan melalui fungsi `perform_rfe_analysis()` yang membuat objek RFE dengan parameter n_features_to_select=1 dan step=1, melakukan fitting terhadap data, dan mengembalikan dictionary berisi ranking fitur.

```python
def perform_rfe_analysis(model, X, y, feature_names):
    rfe = RFE(model, n_features_to_select=1, step=1)
    rfe.fit(X, y)

    # Get feature rankings
    feature_rankings = dict(zip(feature_names, rfe.ranking_))

    return feature_rankings
```

Metode ketiga adalah Systematic Ablation Study yang melakukan validasi feature importance melalui progressive feature removal. Implementasi ablation study dilakukan melalui fungsi `ablation_study()` yang menghitung baseline score, kemudian secara iteratif menghapus satu fitur dan menghitung performa model tanpa fitur tersebut. Impact dari penghapusan fitur dihitung sebagai selisih antara baseline score dan ablated score, yang menunjukkan seberapa penting fitur tersebut.

```python
def ablation_study(model, X, y, feature_names):
    baseline_score = cross_val_score(model, X, y, cv=5).mean()

    ablation_results = []
    for feature in feature_names:
        # Remove feature
        X_ablated = X.drop(columns=[feature])

        # Calculate performance
        ablated_score = cross_val_score(model, X_ablated, y, cv=5).mean()

        # Calculate impact
        impact = baseline_score - ablated_score

        ablation_results.append({
            'feature': feature,
            'baseline_score': baseline_score,
            'ablated_score': ablated_score,
            'impact': impact
        })

    return ablation_results
```

Untuk mencegah data leakage, diterapkan feature filtering melalui kelas `FeatureFilter` yang mendefinisikan dua set fitur: label_creation_features yang berisi fitur-fitur yang digunakan untuk membuat label dan tidak boleh digunakan dalam model, serta safe_model_features yang berisi fitur-fitur yang aman digunakan dalam model machine learning.

```python
class FeatureFilter:
    def __init__(self):
        # Features yang digunakan untuk membuat label (TIDAK BOLEH untuk model)
        self.label_creation_features = {
            'fatigue_risk_score', 'fatigue_risk',
            'stress_count', 'workload_count', 'negative_emotion_count'
        }

        # Features yang AMAN untuk model ML
        self.safe_model_features = {
            'total_distance_km', 'avg_distance_km', 'total_time_minutes',
            'activity_days', 'consistency_score', 'gamification_balance'
        }
```

#### **Tahap 6: Training Model**

Tahap training model menggunakan empat algoritma machine learning yang berbeda untuk membandingkan performa dalam prediksi risiko fatigue. Algoritma pertama adalah Logistic Regression yang dikonfigurasi dengan random_state=42 untuk reproducibility, max_iter=1000 untuk memastikan konvergensi, dan class_weight='balanced' untuk menangani ketidakseimbangan kelas.

```python
LogisticRegression(
    random_state=42,
    max_iter=1000,
    class_weight='balanced'
)
```

Algoritma kedua adalah Random Forest yang dikonfigurasi dengan 100 estimators, random_state=42, class_weight='balanced', dan max_depth=10 untuk mencegah overfitting sambil mempertahankan kemampuan prediksi yang baik.

```python
RandomForestClassifier(
    n_estimators=100,
    random_state=42,
    class_weight='balanced',
    max_depth=10
)
```

Algoritma ketiga adalah Gradient Boosting yang dikonfigurasi dengan 100 estimators, random_state=42, learning_rate=0.1, dan max_depth=6 untuk mengoptimalkan trade-off antara bias dan variance.

```python
GradientBoostingClassifier(
    n_estimators=100,
    random_state=42,
    learning_rate=0.1,
    max_depth=6
)
```

Algoritma keempat adalah XGBoost yang dikonfigurasi dengan parameter serupa dengan Gradient Boosting namun dengan tambahan eval_metric='mlogloss' untuk optimasi yang lebih baik pada masalah klasifikasi multi-kelas.

```python
XGBClassifier(
    n_estimators=100,
    random_state=42,
    learning_rate=0.1,
    max_depth=6,
    eval_metric='mlogloss'
)
```

Proses optimasi hyperparameter dilakukan menggunakan GridSearchCV dengan 5-fold cross-validation untuk memastikan generalisasi yang baik. Parameter grid didefinisikan untuk Random Forest yang mencakup variasi n_estimators (50, 100, 200), max_depth (5, 10, 15, None), min_samples_split (2, 5, 10), dan min_samples_leaf (1, 2, 4). Untuk Gradient Boosting, parameter grid mencakup variasi n_estimators (50, 100, 200), learning_rate (0.01, 0.1, 0.2), max_depth (3, 6, 9), dan subsample (0.8, 0.9, 1.0).

```python
param_grids = {
    'random_forest': {
        'n_estimators': [50, 100, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    },
    'gradient_boosting': {
        'n_estimators': [50, 100, 200],
        'learning_rate': [0.01, 0.1, 0.2],
        'max_depth': [3, 6, 9],
        'subsample': [0.8, 0.9, 1.0]
    }
}
```

#### **Tahap 7: Evaluation Model**

Tahap evaluasi model menggunakan strategi Stratified K-Fold Cross-Validation dengan k=5 untuk mempertahankan distribusi kelas dalam setiap fold, yang penting mengingat adanya ketidakseimbangan dalam distribusi kategori risiko fatigue. Implementasi cross-validation dilakukan dengan menggunakan objek StratifiedKFold dengan parameter n_splits=5, shuffle=True untuk randomisasi data, dan random_state=42 untuk reproducibility. Fungsi cross_validate digunakan untuk mengevaluasi model dengan berbagai metrik sekaligus dan menyimpan skor train dan validation untuk analisis lebih lanjut.

```python
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
cv_scores = cross_validate(
    model, X, y,
    cv=cv,
    scoring=['accuracy', 'f1_macro', 'precision_macro', 'recall_macro'],
    return_train_score=True
)
```

Evaluasi model menggunakan dua kategori metrik: primary metrics dan secondary metrics. Primary metrics meliputi Accuracy yang mengukur proporsi prediksi yang benar secara keseluruhan, F1-Score (Macro) yang merupakan harmonic mean dari precision dan recall yang dirata-ratakan untuk semua kelas, Precision (Macro) yang mengukur rata-rata precision per kelas, dan Recall (Macro) yang mengukur rata-rata recall per kelas. Secondary metrics meliputi ROC-AUC yang mengukur area under ROC curve, Confusion Matrix yang memberikan detail klasifikasi per kelas, dan Classification Report yang menyajikan laporan lengkap performa per kelas.

Untuk mendeteksi overfitting, dilakukan Train-Validation Gap Analysis yang membandingkan performa model pada data training dan validation. Fungsi `detect_overfitting()` menghitung selisih antara rata-rata skor training dan validation, kemudian mengkategorikan tingkat overfitting berdasarkan besarnya gap. Gap > 0.1 dikategorikan sebagai HIGH_OVERFITTING, gap > 0.05 sebagai MODERATE_OVERFITTING, dan gap ≤ 0.05 sebagai LOW_OVERFITTING.

```python
def detect_overfitting(train_scores, val_scores):
    train_mean = np.mean(train_scores)
    val_mean = np.mean(val_scores)
    gap = train_mean - val_mean

    if gap > 0.1:
        return "HIGH_OVERFITTING"
    elif gap > 0.05:
        return "MODERATE_OVERFITTING"
    else:
        return "LOW_OVERFITTING"
```

#### **Tahap 8: Results & Analysis**

Tahap hasil dan analisis merupakan tahap akhir dalam metodologi penelitian yang melibatkan interpretasi komprehensif dari output model machine learning. Analisis hasil mencakup interpretasi performa model terbaik berdasarkan multiple metrics seperti accuracy, F1-score, precision, dan recall, yang memberikan gambaran menyeluruh tentang kemampuan prediksi model. Selain itu, dilakukan analisis feature importance dan kontribusi setiap variabel menggunakan SHAP untuk memahami faktor-faktor yang paling berpengaruh dalam prediksi risiko fatigue. Validasi hipotesis penelitian dilakukan melalui statistical testing untuk memastikan signifikansi temuan, dan identifikasi pattern serta insights dari data dilakukan untuk aplikasi praktis dalam manajemen fatigue mahasiswa.

Visualisasi dan reporting merupakan komponen penting dalam tahap ini, yang mencakup pembuatan confusion matrix dan ROC curves untuk evaluasi klasifikasi secara visual, feature importance plots untuk meningkatkan interpretability model, performance comparison charts untuk membandingkan performa antar algoritma, time series analysis untuk mengidentifikasi pola temporal aktivitas, serta comprehensive research report yang memenuhi academic standards.

Output penelitian yang dihasilkan meliputi model terbaik dengan akurasi optimal untuk prediksi fatigue, feature ranking berdasarkan importance scores dari SHAP analysis yang menunjukkan kontribusi relatif setiap fitur, insights praktis untuk aplikasi real-world dalam student wellness, serta rekomendasi untuk penelitian lanjutan dan implementasi sistem prediksi fatigue.

Implementasi complete analysis pipeline dilakukan melalui kelas `CompleteAnalysisPipeline` yang mengintegrasikan seluruh proses analisis dari data processing hingga visualisasi. Kelas ini memiliki tiga komponen utama: data_processor untuk memproses data mentah, fatigue_classifier untuk melakukan prediksi risiko fatigue, dan visualizer untuk membuat visualisasi penelitian. Metode `run_complete_pipeline()` menjalankan tiga fase analisis secara berurutan: data processing, fatigue prediction, dan visualization, kemudian mengembalikan hasil ML dan data yang telah diproses.

```python
class CompleteAnalysisPipeline:
    def __init__(self, include_ml=True):
        self.data_processor = DataProcessor(raw_data_path="dataset/raw")
        self.visualizer = Visualizer()
        self.fatigue_classifier = FatigueRiskClassifier()

    def run_complete_pipeline(self):
        # Phase 1: Data Processing
        processed_data = self.run_data_processing()

        # Phase 2: Fatigue Prediction
        ml_results = self.run_fatigue_prediction(processed_data)

        # Phase 3: Visualization
        self.create_research_visualizations(processed_data)

        return {'ml_results': ml_results, 'processed_data': processed_data}
```

Pipeline analisis dirancang dengan empat mode yang dapat dipilih sesuai kebutuhan: Complete Pipeline yang menjalankan seluruh proses dari data processing hingga ML analysis, Fatigue Only yang fokus pada fatigue classification, feature selection, dan ML, No ML yang hanya melakukan data processing tanpa analisis machine learning, dan ML Only yang fokus pada pipeline machine learning saja tanpa preprocessing data.

Bias correction framework diterapkan untuk mengatasi potensi bias dalam data dan analisis. Language pattern bias correction dilakukan melalui normalisasi untuk perbedaan bahasa dalam judul aktivitas, standardisasi format waktu dan jarak, serta koreksi untuk variasi cultural dalam deskripsi aktivitas. Activity type bias correction meliputi normalisasi untuk berbagai jenis aktivitas kardiovaskular, standardisasi metrik across different activity types, dan koreksi untuk seasonal variations yang mungkin mempengaruhi pola aktivitas.

Penelitian ini memiliki beberapa keterbatasan metodologi yang perlu diakui. Keterbatasan data meliputi self-reported bias dalam data platform digital yang dapat mempengaruhi akurasi informasi, missing data untuk aktivitas non-digital yang tidak tercatat dalam platform, temporal limitations dalam periode observasi yang relatif terbatas, dan platform dependency pada akurasi sensor dan algoritma yang digunakan oleh aplikasi.

Keterbatasan analisis mencakup cross-sectional design yang membatasi kemampuan untuk melakukan inferensi kausal, sample representativeness yang terbatas pada pengguna teknologi sehingga mungkin tidak mewakili populasi mahasiswa secara keseluruhan, generalizability yang terbatas pada konteks mahasiswa Indonesia, dan validation yang masih terbatas pada internal dataset tanpa validasi eksternal.

Keterbatasan model meliputi feature engineering yang bergantung pada domain knowledge dan mungkin tidak menangkap semua aspek relevan dari fatigue, model interpretability yang terbatas untuk complex algorithms seperti ensemble methods, overfitting risk pada dataset yang relatif kecil, dan belum dilakukannya external validation untuk memastikan generalisasi model pada populasi yang berbeda.

Meskipun memiliki keterbatasan tersebut, metodologi ini dirancang untuk memberikan analisis yang komprehensif dan dapat direplikasi, dengan mempertimbangkan aspek teknis, etis, dan praktis dalam implementasi sistem prediksi fatigue berbasis machine learning. Transparansi mengenai keterbatasan ini penting untuk interpretasi yang tepat terhadap hasil penelitian dan pengembangan penelitian lanjutan.
