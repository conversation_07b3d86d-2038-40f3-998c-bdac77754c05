================================================================================
🔍 SHAP ABLATION STUDY REPORT
================================================================================

📋 DATASET INFORMATION:
   • Dataset Path: dataset/processed/safe_ml_fatigue_dataset.csv
   • Target Column: fatigue_risk
   • Total Features: 18
   • Total Samples: 291
   • Target Distribution: {'medium_risk': 145, 'high_risk': 100, 'low_risk': 46}

🤖 MODEL PERFORMANCE:
   • Logistic Regression:
     - Test Accuracy: 0.5763
     - Test F1-Score: 0.5709
     - CV Accuracy: 0.6208 (±0.0548)
     - CV F1-Score: 0.6160 (±0.0554)
   • Random Forest:
     - Test Accuracy: 0.9322
     - Test F1-Score: 0.9349
     - CV Accuracy: 0.9568 (±0.0273)
     - CV F1-Score: 0.9575 (±0.0269)
   • Gradient Boosting:
     - Test Accuracy: 0.8983
     - Test F1-Score: 0.8972
     - CV Accuracy: 0.9438 (±0.0222)
     - CV F1-Score: 0.9434 (±0.0224)
   • XGBoost:
     - Test Accuracy: 0.9153
     - Test F1-Score: 0.9157
     - CV Accuracy: 0.9222 (±0.0354)
     - CV F1-Score: 0.9211 (±0.0359)

🎯 GLOBAL SHAP FEATURE IMPORTANCE:

   📊 Logistic Regression:
      1. 🔥 productivity_points: 0.2119
      2. 🔥 strava_title_count: 0.1116
      3. 🔸 gamification_balance: 0.0333
      4. 🔸 achievement_rate: 0.0267
      5. 🔸 total_distance_km: 0.0260
      6. 🔸 title_balance_ratio: 0.0210
      7. ▫️ pomokit_title_length: 0.0165
      8. ▫️ strava_title_length: 0.0124
      9. ▫️ strava_unique_words: 0.0101
     10. ▫️ total_title_diversity: 0.0094
     11. ▫️ pomokit_title_count: 0.0089
     12. ▫️ activity_points: 0.0089
     13. ▫️ avg_time_minutes: 0.0083
     14. ▫️ total_time_minutes: 0.0065
     15. ▫️ pomokit_unique_words: 0.0061
     16. ▫️ avg_distance_km: 0.0059
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

   📊 Random Forest:
      1. 🔥 productivity_points: 0.2126
      2. 🔥 strava_title_count: 0.1120
      3. 🔸 gamification_balance: 0.0299
      4. 🔸 achievement_rate: 0.0269
      5. 🔸 total_distance_km: 0.0223
      6. ▫️ title_balance_ratio: 0.0198
      7. ▫️ pomokit_title_length: 0.0157
      8. ▫️ strava_title_length: 0.0123
      9. ▫️ activity_points: 0.0079
     10. ▫️ strava_unique_words: 0.0074
     11. ▫️ pomokit_title_count: 0.0060
     12. ▫️ total_title_diversity: 0.0054
     13. ▫️ total_time_minutes: 0.0052
     14. ▫️ avg_time_minutes: 0.0043
     15. ▫️ avg_distance_km: 0.0035
     16. ▫️ pomokit_unique_words: 0.0013
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

   📊 Gradient Boosting:
      1. 🔥 productivity_points: 0.2130
      2. 🔥 strava_title_count: 0.1117
      3. 🔸 gamification_balance: 0.0302
      4. 🔸 achievement_rate: 0.0269
      5. 🔸 total_distance_km: 0.0219
      6. ▫️ title_balance_ratio: 0.0199
      7. ▫️ pomokit_title_length: 0.0157
      8. ▫️ strava_title_length: 0.0118
      9. ▫️ activity_points: 0.0081
     10. ▫️ strava_unique_words: 0.0079
     11. ▫️ total_time_minutes: 0.0064
     12. ▫️ pomokit_title_count: 0.0051
     13. ▫️ total_title_diversity: 0.0051
     14. ▫️ avg_distance_km: 0.0044
     15. ▫️ avg_time_minutes: 0.0039
     16. ▫️ pomokit_unique_words: 0.0011
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

   📊 XGBoost:
      1. 🔥 productivity_points: 0.2133
      2. 🔥 strava_title_count: 0.1122
      3. 🔸 gamification_balance: 0.0300
      4. 🔸 achievement_rate: 0.0268
      5. 🔸 total_distance_km: 0.0224
      6. ▫️ title_balance_ratio: 0.0196
      7. ▫️ pomokit_title_length: 0.0157
      8. ▫️ strava_title_length: 0.0121
      9. ▫️ strava_unique_words: 0.0083
     10. ▫️ activity_points: 0.0072
     11. ▫️ total_time_minutes: 0.0057
     12. ▫️ pomokit_title_count: 0.0055
     13. ▫️ total_title_diversity: 0.0055
     14. ▫️ avg_time_minutes: 0.0042
     15. ▫️ avg_distance_km: 0.0037
     16. ▫️ pomokit_unique_words: 0.0009
     17. ▫️ avg_cycles: 0.0000
     18. ▫️ weekly_efficiency: 0.0000

🔬 SHAP ANALYSIS ADVANTAGES:
   • Theoretically grounded (Shapley values from game theory)
   • Individual prediction explanations available
   • Captures feature interactions
   • Model-agnostic interpretability
   • Positive/negative contribution analysis

🎖️ FEATURE CONSISTENCY ACROSS ALGORITHMS:
   Most consistent features (appearing in multiple top-5 lists):
     • productivity_points: 4/4 algorithms (100.0%)
     • strava_title_count: 4/4 algorithms (100.0%)
     • gamification_balance: 4/4 algorithms (100.0%)
     • achievement_rate: 4/4 algorithms (100.0%)
     • total_distance_km: 4/4 algorithms (100.0%)

✅ SHAP-BASED RECOMMENDATIONS:
   • Best performing algorithm: Random Forest (0.9322 accuracy)
   • Top 5 SHAP features for production:
     1. productivity_points (SHAP: 0.2126)
     2. strava_title_count (SHAP: 0.1120)
     3. gamification_balance (SHAP: 0.0299)
     4. achievement_rate (SHAP: 0.0269)
     5. total_distance_km (SHAP: 0.0223)

📊 SHAP ANALYSIS SUMMARY:
   • Total algorithms analyzed: 4
   • Total features analyzed: 18
   • Best accuracy achieved: 0.9322
   • Analysis timestamp: 2025-07-21T21:11:45.654381