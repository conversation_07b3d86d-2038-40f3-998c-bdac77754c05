"""
Fatigue Risk Classification System
A standalone module for classifying fatigue risk based on title features
Note: Using 'fatigue' instead of 'fatigue' for safer non-medical analysis
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FatigueRiskClassifier:
    """
    Standalone fatigue risk classifier based on title analysis and behavioral patterns
    Note: Using 'fatigue' terminology for safer non-medical analysis
    """

    def __init__(self):
        """Initialize the fatigue risk classifier"""
        self.stress_keywords = self._define_stress_keywords()
        self.workload_keywords = self._define_workload_keywords()
        self.negative_emotion_keywords = self._define_negative_emotion_keywords()
        self.recovery_keywords = self._define_recovery_keywords()
        self.time_pressure_keywords = self._define_time_pressure_keywords()
        
    def _define_stress_keywords(self) -> List[str]:
        """
        Define stress-related keywords based on psychological literature
        
        Returns:
            List of stress indicator keywords
        """
        return [
            # Direct stress indicators
            'stress', 'stressed', 'pressure', 'overwhelm', 'overwhelmed',
            'anxiety', 'anxious', 'panic', 'worried', 'worry',
            
            # Physical exhaustion indicators
            'tired', 'exhausted', 'fatigue', 'drained',
            'sleepy', 'insomnia', 'restless',
            
            # Cognitive overload indicators
            'confused', 'foggy', 'unclear', 'scattered', 'distracted',
            'forgetful', 'overwhelmed'
        ]
    
    def _define_workload_keywords(self) -> List[str]:
        """
        Define workload-related keywords indicating high work intensity
        
        Returns:
            List of workload indicator keywords
        """
        return [
            # Work-related terms (Indonesian)
            'tugas', 'kerja', 'pekerjaan', 'project', 'proyek',
            'meeting', 'rapat', 'presentasi', 'laporan', 'report',
            
            # Academic workload (Indonesian context)
            'skripsi', 'thesis', 'ujian', 'exam', 'test', 'quiz',
            'assignment', 'homework', 'pr', 'study', 'belajar',
            
            # Work intensity indicators
            'overtime', 'lembur', 'deadline', 'urgent', 'rush',
            'extra', 'additional', 'tambahan', 'more', 'lagi',
            
            # Multiple work indicators
            'multiple', 'banyak', 'several', 'beberapa', 'various'
        ]
    
    def _define_negative_emotion_keywords(self) -> List[str]:
        """
        Define negative emotion keywords indicating psychological distress
        
        Returns:
            List of negative emotion keywords
        """
        return [
            # Frustration and anger
            'frustrated', 'frustasi', 'angry', 'marah', 'upset',
            'annoyed', 'irritated', 'kesal',
            
            # Sadness and depression
            'sad', 'sedih', 'down', 'depressed', 'depresi',
            'hopeless', 'putus asa', 'disappointed', 'kecewa',
            
            # Negative self-evaluation
            'bad', 'buruk', 'terrible', 'awful', 'horrible',
            'failed', 'gagal', 'wrong', 'salah', 'mistake',
            
            # Isolation and loneliness
            'lonely', 'kesepian', 'isolated', 'alone', 'sendiri',
            'empty', 'kosong', 'meaningless', 'tidak bermakna'
        ]
    
    def _define_recovery_keywords(self) -> List[str]:
        """
        Define recovery and positive coping keywords
        
        Returns:
            List of recovery indicator keywords
        """
        return [
            # Rest and relaxation
            'rest', 'istirahat', 'relax', 'santai', 'break', 'rehat',
            'vacation', 'liburan', 'holiday', 'cuti',
            
            # Positive activities
            'fun', 'enjoy', 'senang', 'happy', 'bahagia',
            'good', 'bagus', 'great', 'excellent', 'amazing',
            
            # Mindfulness and wellness
            'meditation', 'meditasi', 'mindful', 'peaceful', 'damai',
            'calm', 'tenang', 'refresh', 'recharge', 'recovery',
            
            # Social support
            'friends', 'teman', 'family', 'keluarga', 'together',
            'bersama', 'support', 'dukungan'
        ]
    
    def _define_time_pressure_keywords(self) -> List[str]:
        """
        Define time pressure keywords indicating urgency and rush
        
        Returns:
            List of time pressure keywords
        """
        return [
            # Urgency indicators
            'urgent', 'mendesak', 'rush', 'terburu', 'hurry',
            'quick', 'cepat', 'fast', 'immediately', 'segera',
            
            # Time constraints
            'deadline', 'late', 'terlambat', 'behind', 'tertinggal',
            'time', 'waktu', 'schedule', 'jadwal',
            
            # Repetitive work indicators
            'again', 'lagi', 'repeat', 'ulang', 'redo', 'revisi',
            'continue', 'lanjut', 'ongoing', 'berlanjut'
        ]
    
    def analyze_title_indicators(self, combined_title: str) -> Dict[str, int]:
        """
        Analyze title for fatigue risk indicators
        
        Args:
            combined_title: Combined title string from activities
            
        Returns:
            Dictionary with indicator counts
        """
        if pd.isna(combined_title) or combined_title == '':
            return {
                'stress_count': 0,
                'workload_count': 0,
                'negative_emotion_count': 0,
                'recovery_count': 0,
                'time_pressure_count': 0
            }
        
        title_lower = str(combined_title).lower()
        
        # Count keyword occurrences
        stress_count = sum(1 for keyword in self.stress_keywords if keyword in title_lower)
        workload_count = sum(1 for keyword in self.workload_keywords if keyword in title_lower)
        negative_count = sum(1 for keyword in self.negative_emotion_keywords if keyword in title_lower)
        recovery_count = sum(1 for keyword in self.recovery_keywords if keyword in title_lower)
        time_pressure_count = sum(1 for keyword in self.time_pressure_keywords if keyword in title_lower)
        
        return {
            'stress_count': stress_count,
            'workload_count': workload_count,
            'negative_emotion_count': negative_count,
            'recovery_count': recovery_count,
            'time_pressure_count': time_pressure_count
        }
    
    def calculate_behavioral_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate behavioral fatigue indicators from activity patterns
        
        Args:
            data: DataFrame with activity data
            
        Returns:
            DataFrame with behavioral indicators added
        """
        df = data.copy()
        
        # Work intensity (cycles per work day)
        df['work_intensity'] = df['total_cycles'] / np.maximum(df['work_days'], 1)
        
        # Work-life imbalance ratio
        df['work_life_imbalance'] = np.where(
            df['activity_days'] == 0,
            10,  # Maximum imbalance if no physical activity
            df['work_days'] / np.maximum(df['activity_days'], 1)
        )
        
        # Activity deficit (lack of physical activity relative to work)
        df['activity_deficit'] = np.maximum(0, df['work_days'] - df['activity_days'])
        
        # Consistency deficit (low overall consistency)
        df['consistency_deficit'] = np.maximum(0, 0.7 - df['consistency_score'])
        
        return df
    
    def calculate_fatigue_risk_score(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate comprehensive fatigue risk score

        Args:
            data: DataFrame with title and behavioral indicators

        Returns:
            DataFrame with fatigue risk score added
        """
        df = data.copy()
        
        # Weighted fatigue risk score (0-100 scale)
        # Higher weights for more critical indicators based on research
        fatigue_score = (
            # Title-based indicators (40% weight)
            df['stress_count'] * 8 +                    # High weight for direct stress
            df['workload_count'] * 5 +                  # Moderate weight for workload
            df['negative_emotion_count'] * 10 +         # High weight for negative emotions
            df['time_pressure_count'] * 6 +             # Moderate-high weight for time pressure
            
            # Behavioral indicators (50% weight)
            (df['work_days'] / 7) * 20 +                # Work frequency impact
            (df['work_intensity'].clip(upper=5) / 5) * 15 +  # Work intensity impact
            (df['work_life_imbalance'].clip(upper=10) / 10) * 12 +  # Work-life balance
            df['activity_deficit'] * 3 +                # Physical activity deficit
            df['consistency_deficit'] * 25 +            # Consistency deficit
            
            # Recovery factors (10% weight - negative contribution)
            - df['recovery_count'] * 8 -                # Recovery activities reduce risk
            - (df['activity_days'] / 7) * 8             # Physical activity reduces risk
        )
        
        # Normalize to 0-100 scale
        df['fatigue_risk_score'] = np.clip(fatigue_score, 0, 100)
        
        return df
    
    def classify_fatigue_risk(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Classify fatigue risk into categories based on multiple criteria
        
        Args:
            data: DataFrame with fatigue risk score
            
        Returns:
            DataFrame with fatigue risk classification
        """
        df = data.copy()
        
        def determine_risk_level(row):
            score = row['fatigue_risk_score']
            work_days = row['work_days']
            activity_days = row['activity_days']
            consistency_score = row['consistency_score']
            stress_count = row['stress_count']
            negative_count = row['negative_emotion_count']
            
            # High risk criteria (multiple pathways)
            high_risk_conditions = [
                score >= 50,                                    # High overall score
                work_days >= 6,                                 # Excessive work frequency
                (work_days >= 4) and (activity_days <= 1),     # Work-life imbalance
                consistency_score <= 0.4,                      # Very low consistency
                (stress_count >= 2) or (negative_count >= 2),  # High psychological distress
                (work_days >= 5) and (row['recovery_count'] == 0)  # High work, no recovery
            ]
            
            if any(high_risk_conditions):
                return 'high_risk'
            
            # Medium risk criteria
            medium_risk_conditions = [
                score >= 25,                                    # Moderate overall score
                work_days >= 4,                                 # High work frequency
                (work_days >= 3) and (activity_days <= 1),     # Moderate work-life imbalance
                consistency_score <= 0.6,                      # Low consistency
                (stress_count >= 1) or (negative_count >= 1),  # Some psychological distress
                activity_days == 0                              # No physical activity
            ]
            
            if any(medium_risk_conditions):
                return 'medium_risk'
            
            # Low risk (default)
            return 'low_risk'
        
        df['fatigue_risk'] = df.apply(determine_risk_level, axis=1)
        
        return df
    
    def add_additional_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Add additional fatigue warning indicators
        
        Args:
            data: DataFrame with fatigue classification
            
        Returns:
            DataFrame with additional indicators
        """
        df = data.copy()
        
        # Workaholic pattern (high work, low activity)
        df['workaholic_pattern'] = np.where(
            (df['work_days'] >= 5) & (df['activity_days'] <= 1),
            'yes', 'no'
        )
        
        # Recovery deficit (high work, no recovery activities)
        df['recovery_deficit'] = np.where(
            (df['recovery_count'] == 0) & (df['work_days'] >= 3),
            'yes', 'no'
        )
        
        # Chronic stress pattern (consistent stress indicators)
        df['chronic_stress_pattern'] = np.where(
            (df['stress_count'] >= 1) & (df['negative_emotion_count'] >= 1),
            'yes', 'no'
        )
        
        # Time pressure syndrome (high urgency indicators)
        df['time_pressure_syndrome'] = np.where(
            df['time_pressure_count'] >= 2,
            'yes', 'no'
        )
        
        return df
    
    def process_fatigue_classification(self, data_path: str) -> pd.DataFrame:
        """
        Complete fatigue risk classification pipeline
        
        Args:
            data_path: Path to the processed dataset
            
        Returns:
            DataFrame with fatigue risk classification
        """
        logger.info("Starting fatigue risk classification pipeline...")
        
        # Load data
        df = pd.read_csv(data_path)
        logger.info(f"Loaded dataset with {len(df)} observations")
        
        # Analyze title indicators
        logger.info("Analyzing title indicators...")
        title_indicators = df['combined_titles'].apply(self.analyze_title_indicators)
        
        # Extract indicator counts
        for indicator in ['stress_count', 'workload_count', 'negative_emotion_count', 
                         'recovery_count', 'time_pressure_count']:
            df[indicator] = [result[indicator] for result in title_indicators]
        
        # Calculate behavioral indicators
        logger.info("Calculating behavioral indicators...")
        df = self.calculate_behavioral_indicators(df)
        
        # Calculate fatigue risk score
        logger.info("Calculating fatigue risk score...")
        df = self.calculate_fatigue_risk_score(df)
        
        # Classify fatigue risk
        logger.info("Classifying fatigue risk levels...")
        df = self.classify_fatigue_risk(df)
        
        # Add additional indicators
        logger.info("Adding additional fatigue indicators...")
        df = self.add_additional_indicators(df)
        
        logger.info("Fatigue risk classification completed successfully")
        
        return df
    
    def generate_classification_summary(self, data: pd.DataFrame) -> Dict:
        """
        Generate summary statistics for fatigue classification
        
        Args:
            data: DataFrame with fatigue classification
            
        Returns:
            Dictionary with summary statistics
        """
        summary = {
            'total_observations': len(data),
            'fatigue_distribution': data['fatigue_risk'].value_counts().to_dict(),
            'fatigue_percentages': (data['fatigue_risk'].value_counts(normalize=True) * 100).round(1).to_dict(),
            'average_risk_score': data['fatigue_risk_score'].mean(),
            'risk_score_std': data['fatigue_risk_score'].std(),
            'warning_indicators': {
                'workaholic_pattern': (data['workaholic_pattern'] == 'yes').sum(),
                'recovery_deficit': (data['recovery_deficit'] == 'yes').sum(),
                'chronic_stress_pattern': (data['chronic_stress_pattern'] == 'yes').sum(),
                'time_pressure_syndrome': (data['time_pressure_syndrome'] == 'yes').sum()
            },
            'title_indicators_avg': {
                'stress_count': data['stress_count'].mean(),
                'workload_count': data['workload_count'].mean(),
                'negative_emotion_count': data['negative_emotion_count'].mean(),
                'recovery_count': data['recovery_count'].mean(),
                'time_pressure_count': data['time_pressure_count'].mean()
            }
        }
        
        return summary


def main():
    """Main function for testing the fatigue classifier"""
    # Initialize classifier
    classifier = FatigueRiskClassifier()
    
    # Process classification
    data_path = "dataset/processed/weekly_merged_dataset_with_gamification.csv"
    
    if not Path(data_path).exists():
        print(f"❌ Dataset not found: {data_path}")
        print("Please run the main pipeline first to generate the processed dataset.")
        return
    
    # Run classification
    classified_data = classifier.process_fatigue_classification(data_path)
    
    # Generate summary
    summary = classifier.generate_classification_summary(classified_data)
    
    # Save results
    output_path = "dataset/processed/fatigue_risk_classified_dataset.csv"
    classified_data.to_csv(output_path, index=False)
    
    print("🎉 Fatigue Risk Classification Completed!")
    print(f"📊 Results saved to: {output_path}")
    print(f"📈 Dataset shape: {classified_data.shape}")
    print(f"🔍 Fatigue distribution: {summary['fatigue_percentages']}")
    
    return classified_data, summary


if __name__ == "__main__":
    main()
