#!/usr/bin/env python3
"""
Feature Filter untuk mencegah data leakage
Memisahkan fitur pembuat label dari fitur yang aman untuk model ML

Modul ini dirancang untuk bekerja dengan berbagai classifier:
- fatigue_classifier.py (output: fatigue_risk_classified_dataset.csv)
- title_only_fatigue_classifier.py (output: fatigue_classified_with_title_only.csv)
- bias_corrected_title_classifier.py (output: bias_corrected_fatigue_classified_cleaned.csv)

PRINSIP UTAMA:
1. Fitur yang digunakan untuk MEMBUAT LABEL tidak boleh digunakan untuk model ML
2. <PERSON>ya fitur yang benar-benar independen dari proses labeling yang aman
3. Fitur metadata hanya untuk identifikasi, bukan untuk prediksi

CONTOH PENGGUNAAN:
```python
from src.feature_filter import FeatureFilter

# Untuk output fatigue_classifier.py
filter_obj = FeatureFilter()
safe_dataset = filter_obj.create_safe_dataset_for_fatigue_classifier(
    'dataset/processed/fatigue_risk_classified_dataset.csv',
    'dataset/processed/safe_ml_dataset.csv',
    target_column='fatigue_risk'
)

# Validasi fitur sebelum training
features = ['total_distance_km', 'avg_distance_km', 'activity_points']
validation = filter_obj.validate_features_for_ml(features)
if validation['is_safe']:
    print("✅ Fitur aman untuk ML")
else:
    print(f"🚨 Fitur berbahaya: {validation['dangerous_features']}")
```
"""

import pandas as pd
import logging
from typing import List, Dict, Tuple

logger = logging.getLogger(__name__)

class FeatureFilter:
    """
    Filter untuk memisahkan fitur pembuat label dari fitur model
    """
    
    def __init__(self):
        """Initialize feature filter dengan definisi fitur"""
        
        # Fitur yang digunakan untuk MEMBUAT LABEL (TIDAK BOLEH digunakan untuk model)
        self.label_creation_features = {
            'fatigue_risk',
            'activity_days',
            'total_cycles',
            'consistency_score',
            'stress_count',
            'workload_count',
            'negative_emotion_count',
            'recovery_count',
            'time_pressure_count',
            'work_intensity',
            'work_life_imbalance',
            'activity_deficit',
            'consistency_deficit',
            'fatigue_risk_score',
            'workaholic_pattern',
            'recovery_deficit',
            'chronic_stress_pattern',
            'time_pressure_syndrome',
        }
        
        # Fitur metadata (untuk identifikasi, bukan untuk model)
        self.metadata_features = {
            'identity',
            'year_week',
            'strava_activities_titles',
            'pomokit_activities_titles',
            'combined_titles'
        }
        
        # Fitur yang AMAN untuk model ML
        self.safe_model_features = {
            'total_distance_km',
            'avg_distance_km',
            'total_time_minutes',
            'avg_time_minutes',
            'avg_cycles',
            'weekly_efficiency',
            'strava_title_count',
            'strava_title_length',
            'strava_unique_words',
            'pomokit_title_count',
            'pomokit_title_length',
            'pomokit_unique_words',
            'total_title_diversity',
            'title_balance_ratio',
            'activity_points',
            'productivity_points',
            'achievement_rate',
            'gamification_balance',
        }
    
    def filter_features_for_ml(self, df: pd.DataFrame, target_column: str = 'corrected_fatigue_risk') -> Tuple[pd.DataFrame, List[str], Dict[str, List[str]]]:
        """
        Filter dataset untuk ML, menghilangkan fitur pembuat label
        
        Args:
            df: DataFrame input
            target_column: Nama kolom target
            
        Returns:
            Tuple of (filtered_df, safe_features, removed_features_info)
        """
        logger.info("🔍 Filtering features untuk mencegah data leakage...")
        
        # Identifikasi fitur yang ada di dataset
        available_features = set(df.columns)
        
        # Kategorisasi fitur
        found_label_features = available_features.intersection(self.label_creation_features)
        found_metadata_features = available_features.intersection(self.metadata_features)
        found_safe_features = available_features.intersection(self.safe_model_features)
        
        # Fitur yang tidak terkategorikan (perlu review manual)
        uncategorized_features = available_features - found_label_features - found_metadata_features - found_safe_features
        if target_column in uncategorized_features:
            uncategorized_features.remove(target_column)
        
        # Fitur yang akan digunakan untuk model
        model_features = list(found_safe_features)
        
        # Tambahkan fitur uncategorized dengan hati-hati (setelah review)
        # Untuk saat ini, kita exclude semua uncategorized untuk safety
        
        # Buat filtered dataset
        columns_to_keep = model_features + [target_column]
        filtered_df = df[columns_to_keep].copy()
        
        # Info tentang fitur yang dihilangkan
        removed_features_info = {
            'label_creation_features': list(found_label_features),
            'metadata_features': list(found_metadata_features),
            'uncategorized_features': list(uncategorized_features)
        }
        
        # Log hasil filtering
        logger.info(f"📊 Feature filtering results:")
        logger.info(f"   • Total features in dataset: {len(available_features)}")
        logger.info(f"   • Safe model features: {len(model_features)}")
        logger.info(f"   • Label creation features (REMOVED): {len(found_label_features)}")
        logger.info(f"   • Metadata features (REMOVED): {len(found_metadata_features)}")
        logger.info(f"   • Uncategorized features (REMOVED): {len(uncategorized_features)}")
        logger.info(f"   • Final dataset shape: {filtered_df.shape}")
        
        if found_label_features:
            logger.warning(f"⚠️  REMOVED label creation features: {sorted(found_label_features)}")
        
        if uncategorized_features:
            logger.warning(f"⚠️  REMOVED uncategorized features: {sorted(uncategorized_features)}")
            logger.warning(f"   Please review these features manually to determine if they're safe for ML")
        
        return filtered_df, model_features, removed_features_info
    
    def validate_features_for_ml(self, features: List[str]) -> Dict[str, List[str]]:
        """
        Validasi daftar fitur untuk memastikan tidak ada data leakage
        
        Args:
            features: List fitur yang akan digunakan
            
        Returns:
            Dict dengan hasil validasi
        """
        features_set = set(features)
        
        # Cek fitur berbahaya
        dangerous_features = features_set.intersection(self.label_creation_features)
        metadata_features = features_set.intersection(self.metadata_features)
        safe_features = features_set.intersection(self.safe_model_features)
        uncategorized_features = features_set - dangerous_features - metadata_features - safe_features
        
        validation_result = {
            'safe_features': list(safe_features),
            'dangerous_features': list(dangerous_features),
            'metadata_features': list(metadata_features),
            'uncategorized_features': list(uncategorized_features),
            'is_safe': len(dangerous_features) == 0
        }
        
        return validation_result
    
    def get_feature_explanation(self, feature_name: str) -> str:
        """
        Berikan penjelasan tentang kategori fitur
        
        Args:
            feature_name: Nama fitur
            
        Returns:
            Penjelasan kategori fitur
        """
        if feature_name in self.label_creation_features:
            return "🚨 LABEL CREATION - Fitur ini digunakan untuk membuat label, TIDAK BOLEH digunakan untuk model ML"
        elif feature_name in self.metadata_features:
            return "🏷️ METADATA - Fitur identifikasi, tidak untuk model ML"
        elif feature_name in self.safe_model_features:
            return "✅ SAFE - Fitur aman untuk model ML"
        else:
            return "❓ UNCATEGORIZED - Perlu review manual untuk menentukan keamanan"
    
    def create_safe_dataset_for_fatigue_classifier(self, input_path: str, output_path: str, target_column: str = 'fatigue_risk') -> str:
        """
        Buat dataset yang aman untuk ML dari output fatigue_classifier.py

        Args:
            input_path: Path file input (output dari fatigue_classifier.py)
            output_path: Path file output yang aman untuk ML
            target_column: Nama kolom target (default: 'fatigue_risk')

        Returns:
            Path file output yang dibuat
        """
        logger.info(f"📂 Creating safe ML dataset from fatigue_classifier output: {input_path}")

        # Load dataset
        df = pd.read_csv(input_path)
        logger.info(f"📊 Loaded dataset: {df.shape}")

        # Filter features untuk fatigue_classifier output
        safe_df, safe_features, removed_info = self.filter_features_for_ml(df, target_column)

        # Save safe dataset
        safe_df.to_csv(output_path, index=False)
        logger.info(f"💾 Safe dataset saved to: {output_path}")
        logger.info(f"📊 Safe dataset shape: {safe_df.shape}")

        # Print summary
        print(f"\n🛡️  SAFE ML DATASET CREATED (from fatigue_classifier)")
        print(f"📁 Input: {input_path}")
        print(f"📁 Output: {output_path}")
        print(f"📊 Shape: {df.shape} → {safe_df.shape}")
        print(f"✅ Safe features: {len(safe_features)}")
        print(f"🚨 Removed dangerous features: {len(removed_info['label_creation_features'])}")
        print(f"🎯 Target column: {target_column}")

        return output_path

    def create_safe_dataset(self, input_path: str, output_path: str, target_column: str = 'corrected_fatigue_risk') -> str:
        """
        Buat dataset yang aman untuk ML dari file input

        Args:
            input_path: Path file input
            output_path: Path file output
            target_column: Nama kolom target

        Returns:
            Path file output yang dibuat
        """
        logger.info(f"📂 Creating safe ML dataset from: {input_path}")

        # Load dataset
        df = pd.read_csv(input_path)
        logger.info(f"📊 Loaded dataset: {df.shape}")

        # Filter features
        safe_df, safe_features, removed_info = self.filter_features_for_ml(df, target_column)

        # Save safe dataset
        safe_df.to_csv(output_path, index=False)
        logger.info(f"💾 Safe dataset saved to: {output_path}")
        logger.info(f"📊 Safe dataset shape: {safe_df.shape}")

        # Print summary
        print(f"\n🛡️  SAFE ML DATASET CREATED")
        print(f"📁 Input: {input_path}")
        print(f"📁 Output: {output_path}")
        print(f"📊 Shape: {df.shape} → {safe_df.shape}")
        print(f"✅ Safe features: {len(safe_features)}")
        print(f"🚨 Removed dangerous features: {len(removed_info['label_creation_features'])}")

        return output_path


def main():
    """Test feature filter dengan berbagai skenario"""
    filter_obj = FeatureFilter()

    print("🧪 Testing Feature Filter untuk berbagai classifier...")

    # Test 1: Untuk fatigue_classifier.py output
    print("\n1️⃣ Testing dengan output fatigue_classifier.py:")
    fatigue_input = "dataset/processed/fatigue_risk_classified_dataset.csv"
    fatigue_output = "dataset/processed/safe_ml_fatigue_dataset.csv"

    try:
        safe_path = filter_obj.create_safe_dataset_for_fatigue_classifier(
            fatigue_input, fatigue_output, target_column='fatigue_risk'
        )
        print(f"✅ Safe ML dataset created: {safe_path}")
    except Exception as e:
        print(f"❌ Error with fatigue_classifier output: {e}")

    # Test 2: Untuk title_only_fatigue_classifier.py output
    print("\n2️⃣ Testing dengan output title_only_fatigue_classifier.py:")
    title_input = "dataset/processed/fatigue_classified_with_title_only.csv"
    title_output = "dataset/processed/safe_ml_title_only_dataset.csv"

    try:
        safe_path = filter_obj.create_safe_dataset_for_fatigue_classifier(
            title_input, title_output, target_column='title_fatigue_risk'
        )
        print(f"✅ Safe ML dataset created: {safe_path}")
    except Exception as e:
        print(f"❌ Error with title_only output: {e}")

    # Test 3: Untuk bias corrected dataset (default)
    print("\n3️⃣ Testing dengan bias corrected dataset:")
    bias_input = "dataset/processed/fatigue_risk_classified_dataset.csv"
    bias_output = "dataset/processed/safe_ml_bias_corrected_dataset.csv"

    try:
        safe_path = filter_obj.create_safe_dataset(bias_input, bias_output)
        print(f"✅ Safe ML dataset created: {safe_path}")
    except Exception as e:
        print(f"❌ Error with bias corrected dataset: {e}")

    # Test 4: Validasi fitur
    print("\n4️⃣ Testing feature validation:")
    test_features = [
        'total_distance_km', 'avg_distance_km',  # Safe
        'stress_count', 'fatigue_risk_score',    # Dangerous
        'identity', 'year_week',                 # Metadata
        'unknown_feature'                        # Uncategorized
    ]

    validation = filter_obj.validate_features_for_ml(test_features)
    print(f"🔍 Validation results:")
    print(f"   ✅ Safe: {validation['safe_features']}")
    print(f"   🚨 Dangerous: {validation['dangerous_features']}")
    print(f"   🏷️ Metadata: {validation['metadata_features']}")
    print(f"   ❓ Uncategorized: {validation['uncategorized_features']}")
    print(f"   🛡️ Is safe for ML: {validation['is_safe']}")


if __name__ == "__main__":
    main()
