{"algorithm_name": "Logistic Regression", "accuracy": 0.576271186440678, "f1_score": 0.5709430521052797, "cv_accuracy_mean": 0.6208140610545791, "cv_accuracy_std": 0.0548062817328601, "cv_f1_mean": 0.6160262206092593, "cv_f1_std": 0.05544244748501834, "feature_count": 18, "timestamp": "2025-07-21T23:47:11.123615", "model_type": "Pipeline", "algorithm_key": "logistic_regression", "random_state": 42, "dataset_path": "dataset/processed/safe_ml_fatigue_dataset.csv", "target_column": "fatigue_risk", "train_samples": 232, "test_samples": 59}